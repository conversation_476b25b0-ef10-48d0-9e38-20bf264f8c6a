import{a as dt,b as mt,c as pt,d as ut,e as _t}from"./chunk-LMT2ZV2T.js";import{b as ct}from"./chunk-EMQER2I7.js";import{A as ot,B as rt,C as at,D as st,E as lt,c as X,d as Z,e as J,g as $e,j as qe,k as ze,l as He,m as Le,n as Ge,o as Qe,p as Ye,q as We,r as Xe,s as Ze,t as Je,u as Ke,v as ae,w as et,x as tt,y as it,z as nt}from"./chunk-HEB4POL5.js";import{a as Fe,b as Be,c as Ue,e as Ne}from"./chunk-YSBVITGM.js";import{a as Oe,b as Ae}from"./chunk-OFTCLERB.js";import{a as je}from"./chunk-EACHO2FA.js";import"./chunk-ORDMVBIZ.js";import{a as W,b as O}from"./chunk-WSXVBUWR.js";import"./chunk-IT7B5FSQ.js";import"./chunk-3MEMYPGR.js";import"./chunk-EO3PEVGH.js";import{a as Q,b as Y}from"./chunk-553Y2ERR.js";import"./chunk-3VEHVC57.js";import"./chunk-Q34CP4BD.js";import{c as Ce}from"./chunk-3EEDYH74.js";import{C as Ie,F as Ve,H as Te,I as De,J as A,b as xe,d as M,f as be,g as Se,k as ye,n as Me,o as Re,s as we,u as ke,w as Pe,x as Ee}from"./chunk-AG3SD6JT.js";import{Ba as L,Bc as ve,Bd as G,Dc as he,Eb as $,Fb as q,Fc as P,Gb as z,Gd as T,Ja as oe,Jd as D,Kb as r,La as l,Lb as S,Ld as F,Mb as y,Md as B,Nd as U,Pa as b,Qd as N,Rd as E,Tb as re,Wa as k,Wb as R,Yb as w,Z as de,ab as p,ca as me,g as ce,hb as c,ic as ue,jc as _e,la as _,ma as g,qb as n,rb as o,sb as h,tb as C,tc as ge,ub as x,uc as fe,va as pe,vc as V,wb as v,yb as u,zb as m}from"./chunk-ST4QC4E3.js";import"./chunk-X5YLR3NI.js";import{a as I,b as j,i as f}from"./chunk-ODN5LVDJ.js";var K=class i{constructor(t){this.authService=t;this.supabase=t.supabase,this.initializeMockVehicles()}supabase;vehiclesSubject=new ce([]);vehicles$=this.vehiclesSubject.asObservable();initializeMockVehicles(){let t=[{id:"1",driver_id:"driver1",make:"Toyota",model:"Camry",year:2020,color:"Silver",license_plate:"ABC123",capacity:4,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:"2",driver_id:"driver2",make:"Honda",model:"Accord",year:2019,color:"Black",license_plate:"XYZ789",capacity:5,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}];this.vehiclesSubject.next(t)}getDriverVehicles(t){return f(this,null,function*(){try{return this.vehiclesSubject.value.filter(e=>e.driver_id===t)}catch(e){return console.error("Error fetching driver vehicles:",e),[]}})}addVehicle(t){return f(this,null,function*(){try{let e=j(I({},t),{id:Math.random().toString(36).substring(2,9),created_at:new Date().toISOString(),updated_at:new Date().toISOString()}),a=this.vehiclesSubject.value;return this.vehiclesSubject.next([...a,e]),e}catch(e){return console.error("Error adding vehicle:",e),null}})}updateVehicle(t,e){return f(this,null,function*(){try{let s=this.vehiclesSubject.value.map(d=>d.id===t?j(I(I({},d),e),{updated_at:new Date().toISOString()}):d);return this.vehiclesSubject.next(s),!0}catch(a){return console.error("Error updating vehicle:",a),!1}})}deleteVehicle(t){return f(this,null,function*(){try{let a=this.vehiclesSubject.value.filter(s=>s.id!==t);return this.vehiclesSubject.next(a),!0}catch(e){return console.error("Error deleting vehicle:",e),!1}})}static \u0275fac=function(e){return new(e||i)(me(A))};static \u0275prov=de({token:i,factory:i.\u0275fac,providedIn:"root"})};function yt(i,t){i&1&&(n(0,"mat-error"),r(1," Full name is required "),o())}function Mt(i,t){i&1&&(n(0,"mat-error"),r(1," Phone number is required "),o())}var te=class i{constructor(t,e,a,s){this.fb=t;this.authService=e;this.vehicleService=a;this.snackBar=s;this.profileForm=this.fb.group({full_name:["",M.required],phone:["",M.required]}),this.vehicleForm=this.fb.group({make:["",M.required],model:["",M.required],year:["",[M.required,M.min(1990),M.max(this.currentYear)]],color:["",M.required],license_plate:["",M.required],capacity:[4,[M.required,M.min(1),M.max(10)]]})}profileForm;vehicleForm;vehicles=[];currentUser=null;editingVehicle=null;currentYear=new Date().getFullYear();ngOnInit(){this.loadUserProfile(),this.loadVehicles()}loadUserProfile(){return f(this,null,function*(){try{this.currentUser=yield this.authService.getCurrentUser(),this.currentUser&&this.profileForm.patchValue({full_name:this.currentUser.full_name||"",phone:this.currentUser.phone||""})}catch(t){console.error("Error loading user profile:",t),this.snackBar.open("Failed to load profile","Close",{duration:3e3})}})}loadVehicles(){return f(this,null,function*(){try{this.currentUser&&(this.vehicles=yield this.vehicleService.getDriverVehicles(this.currentUser.id))}catch(t){console.error("Error loading vehicles:",t),this.snackBar.open("Failed to load vehicles","Close",{duration:3e3})}})}updateProfile(){return f(this,null,function*(){if(!this.profileForm.invalid)try{if(yield this.authService.updateProfile(this.profileForm.value))this.snackBar.open("Profile updated successfully","Close",{duration:3e3});else throw new Error("Failed to update profile")}catch(t){console.error("Error updating profile:",t),this.snackBar.open("Failed to update profile","Close",{duration:3e3})}})}editVehicle(t){this.editingVehicle=t,this.vehicleForm.patchValue({make:t.make,model:t.model,year:t.year,color:t.color,license_plate:t.license_plate,capacity:t.capacity})}cancelEdit(){this.editingVehicle=null,this.vehicleForm.reset({capacity:4})}saveVehicle(){return f(this,null,function*(){if(!(this.vehicleForm.invalid||!this.currentUser))try{if(this.editingVehicle)if(yield this.vehicleService.updateVehicle(this.editingVehicle.id,this.vehicleForm.value))this.snackBar.open("Vehicle updated successfully","Close",{duration:3e3}),this.cancelEdit(),this.loadVehicles();else throw new Error("Failed to update vehicle");else{let t=j(I({},this.vehicleForm.value),{driver_id:this.currentUser.id});if(yield this.vehicleService.addVehicle(t))this.snackBar.open("Vehicle added successfully","Close",{duration:3e3}),this.vehicleForm.reset({capacity:4}),this.loadVehicles();else throw new Error("Failed to add vehicle")}}catch(t){console.error("Error saving vehicle:",t),this.snackBar.open("Failed to save vehicle","Close",{duration:3e3})}})}deleteVehicle(t){return f(this,null,function*(){try{if(yield this.vehicleService.deleteVehicle(t))this.snackBar.open("Vehicle deleted successfully","Close",{duration:3e3}),this.loadVehicles();else throw new Error("Failed to delete vehicle")}catch(e){console.error("Error deleting vehicle:",e),this.snackBar.open("Failed to delete vehicle","Close",{duration:3e3})}})}static \u0275fac=function(e){return new(e||i)(b(we),b(A),b(K),b(Q))};static \u0275cmp=k({type:i,selectors:[["app-driver-profile"]],decls:19,vars:4,consts:[[1,"profile-container"],[3,"ngSubmit","formGroup"],["appearance","outline",1,"full-width"],["matInput","","formControlName","full_name","placeholder","Enter your full name"],[4,"ngIf"],["matInput","","formControlName","phone","placeholder","Enter your phone number"],["mat-raised-button","","color","primary","type","submit",3,"disabled"]],template:function(e,a){if(e&1&&(n(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),r(4,"Driver Profile"),o()(),n(5,"mat-card-content")(6,"form",1),u("ngSubmit",function(){return a.updateProfile()}),n(7,"mat-form-field",2)(8,"mat-label"),r(9,"Full Name"),o(),h(10,"input",3),p(11,yt,2,0,"mat-error",4),o(),n(12,"mat-form-field",2)(13,"mat-label"),r(14,"Phone Number"),o(),h(15,"input",5),p(16,Mt,2,0,"mat-error",4),o(),n(17,"button",6),r(18," Update Profile "),o()()()()()),e&2){let s,d;l(6),c("formGroup",a.profileForm),l(5),c("ngIf",(s=a.profileForm.get("full_name"))==null?null:s.hasError("required")),l(5),c("ngIf",(d=a.profileForm.get("phone"))==null?null:d.hasError("required")),l(),c("disabled",a.profileForm.invalid||a.profileForm.pristine)}},dependencies:[P,V,ke,ye,xe,be,Se,Me,Re,E,F,U,N,B,Ve,Ie,Pe,Ee,De,Te,D,T,Y,Ne,O],styles:[".profile-container[_ngcontent-%COMP%]{padding:20px;max-width:800px;margin:0 auto}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.vehicle-card[_ngcontent-%COMP%]{margin-top:20px}.vehicle-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 0;border-bottom:1px solid #eee}.vehicle-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.vehicle-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;font-weight:500}.vehicle-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;color:#666}.vehicle-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.divider[_ngcontent-%COMP%]{margin:16px 0}.vehicle-form[_ngcontent-%COMP%]{margin-top:16px}.form-row[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:8px}.form-row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{flex:1}.form-actions[_ngcontent-%COMP%]{display:flex;gap:8px;margin-top:16px}.no-vehicles[_ngcontent-%COMP%]{padding:16px 0;color:#666;font-style:italic}"]})};function wt(i,t){if(i&1&&(n(0,"div",18)(1,"p")(2,"strong"),r(3,"Distance:"),o(),r(4),o(),n(5,"p")(6,"strong"),r(7,"Estimated Time:"),o(),r(8),o()()),i&2){let e=m(2);l(4),y(" ",e.ride.distance_miles," miles"),l(4),y(" ",e.ride.duration_minutes," minutes")}}function kt(i,t){if(i&1){let e=v();n(0,"div",1),u("click",function(){_(e);let s=m();return g(s.closeNavigation())}),n(1,"mat-card",2),u("click",function(s){return _(e),g(s.stopPropagation())}),n(2,"mat-card-header")(3,"mat-card-title"),r(4,"Navigation"),o(),n(5,"button",3),u("click",function(){_(e);let s=m();return g(s.closeNavigation())}),n(6,"mat-icon"),r(7,"close"),o()()(),n(8,"mat-card-content")(9,"div",4)(10,"div",5)(11,"div",6)(12,"mat-icon",7),r(13,"location_on"),o(),n(14,"div",8)(15,"span",9),r(16,"Pickup Location:"),o(),n(17,"span",10),r(18),o()()(),n(19,"div",6)(20,"mat-icon",11),r(21,"flag"),o(),n(22,"div",8)(23,"span",9),r(24,"Dropoff Location:"),o(),n(25,"span",10),r(26),o()()()(),h(27,"app-map-display",12),p(28,wt,9,2,"div",13),n(29,"div",14)(30,"a",15)(31,"button",16)(32,"mat-icon"),r(33,"navigation"),o(),r(34," Navigate to Pickup "),o()(),n(35,"a",15)(36,"button",17)(37,"mat-icon"),r(38,"navigation"),o(),r(39," Navigate to Dropoff "),o()()()()()()()}if(i&2){let e=m();l(18),S(e.ride.pickup_location),l(8),S(e.ride.dropoff_location),l(),c("origin",e.ride.pickup_location)("destination",e.ride.dropoff_location),l(),c("ngIf",e.ride.distance_miles&&e.ride.duration_minutes),l(2),c("href",e.googleMapsPickupUrl,oe),l(5),c("href",e.googleMapsDropoffUrl,oe)}}var ie=class i{constructor(t){this.locationService=t}ride=null;close=new pe;googleMapsPickupUrl="";googleMapsDropoffUrl="";ngOnInit(){this.generateNavigationLinks()}generateNavigationLinks(){this.ride&&(this.googleMapsPickupUrl=this.locationService.getGoogleMapsUrl(this.ride.pickup_location),this.googleMapsDropoffUrl=this.locationService.getGoogleMapsUrl(this.ride.dropoff_location))}closeNavigation(){this.close.emit()}static \u0275fac=function(e){return new(e||i)(b(Oe))};static \u0275cmp=k({type:i,selectors:[["app-ride-navigation"]],inputs:{ride:"ride"},outputs:{close:"close"},decls:1,vars:1,consts:[["class","navigation-overlay",3,"click",4,"ngIf"],[1,"navigation-overlay",3,"click"],[1,"navigation-card",3,"click"],["mat-icon-button","",1,"close-button",3,"click"],[1,"navigation-details"],[1,"location-info"],[1,"location-item"],[1,"location-icon","pickup"],[1,"location-text"],[1,"location-label"],[1,"location-value"],[1,"location-icon","dropoff"],[3,"origin","destination"],["class","route-info",4,"ngIf"],[1,"navigation-links"],["target","_blank",1,"nav-link",3,"href"],["mat-raised-button","","color","primary"],["mat-raised-button","","color","accent"],[1,"route-info"]],template:function(e,a){e&1&&p(0,kt,40,7,"div",0),e&2&&c("ngIf",a.ride)},dependencies:[P,V,E,F,U,N,B,D,T,G,O,W,Ae],styles:[".navigation-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.navigation-card[_ngcontent-%COMP%]{width:90%;max-width:600px;max-height:80vh;overflow-y:auto}.close-button[_ngcontent-%COMP%]{position:absolute;right:8px;top:8px}.navigation-details[_ngcontent-%COMP%]{padding:16px 0}.location-info[_ngcontent-%COMP%]{margin-bottom:24px}.location-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;margin-bottom:16px}.location-icon[_ngcontent-%COMP%]{margin-right:16px;color:#3f51b5}.location-icon.pickup[_ngcontent-%COMP%]{color:#4caf50}.location-icon.dropoff[_ngcontent-%COMP%]{color:#f44336}.location-text[_ngcontent-%COMP%]{display:flex;flex-direction:column}.location-label[_ngcontent-%COMP%]{font-weight:500;margin-bottom:4px}.location-value[_ngcontent-%COMP%]{color:#666}.route-info[_ngcontent-%COMP%]{background-color:#f5f5f5;border-radius:4px;padding:16px;margin-bottom:24px}.route-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0}.navigation-links[_ngcontent-%COMP%]{display:flex;justify-content:space-around;flex-wrap:wrap;gap:16px}.nav-link[_ngcontent-%COMP%]{text-decoration:none}"]})};var Et=["availableSort"],It=["availablePaginator"],Vt=["myRidesSort"],Tt=["myRidesPaginator"],Dt=()=>[3,5,10,25,100],Ot=()=>[5,10,25,100];function At(i,t){i&1&&(n(0,"div",17),h(1,"mat-spinner",18),n(2,"p"),r(3,"Loading rides..."),o()())}function Ft(i,t){i&1&&(n(0,"div",19)(1,"p"),r(2,"No available ride requests at this time."),o()())}function Bt(i,t){i&1&&(n(0,"th",36),r(1,"Pickup"),o())}function Ut(i,t){if(i&1&&(n(0,"td",37),r(1),o()),i&2){let e=t.$implicit;l(),S(e.pickup_location)}}function Nt(i,t){i&1&&(n(0,"th",36),r(1,"Destination"),o())}function jt(i,t){if(i&1&&(n(0,"td",37),r(1),o()),i&2){let e=t.$implicit;l(),S(e.dropoff_location)}}function $t(i,t){i&1&&(n(0,"th",36),r(1,"Time"),o())}function qt(i,t){if(i&1&&(n(0,"td",37),r(1),R(2,"date"),o()),i&2){let e=t.$implicit;l(),S(w(2,1,e.pickup_time,"short"))}}function zt(i,t){i&1&&(n(0,"th",36),r(1,"Fare"),o())}function Ht(i,t){if(i&1&&(n(0,"td",37),r(1),R(2,"currency"),o()),i&2){let e=t.$implicit;l(),S(e.fare?w(2,1,e.fare*.7,"USD"):"TBD")}}function Lt(i,t){i&1&&(n(0,"th",38),r(1,"Actions"),o())}function Gt(i,t){if(i&1){let e=v();n(0,"td",37)(1,"button",39),u("click",function(){let s=_(e).$implicit,d=m(2);return g(d.acceptRide(s.id))}),r(2," Accept "),o()()}}function Qt(i,t){i&1&&h(0,"tr",40)}function Yt(i,t){i&1&&h(0,"tr",41)}function Wt(i,t){if(i&1){let e=v();n(0,"mat-expansion-panel")(1,"mat-expansion-panel-header")(2,"mat-panel-description"),r(3),o(),n(4,"mat-panel-title"),r(5),R(6,"date"),o()(),n(7,"div",42)(8,"p")(9,"strong"),r(10,"To:"),o(),r(11),o(),n(12,"p")(13,"strong"),r(14,"Fare:"),o(),r(15),R(16,"currency"),o()(),n(17,"mat-action-row")(18,"button",39),u("click",function(){let s=_(e).$implicit,d=m(2);return g(d.acceptRide(s.id))}),r(19," Accept "),o()()()}if(i&2){let e=t.$implicit;l(3),y(" ",e.pickup_location," "),l(2),y(" ",w(6,4,e.pickup_time,"shortTime")," "),l(6),y(" ",e.dropoff_location,""),l(4),y(" ",e.fare?w(16,7,e.fare*.7,"USD"):"TBD","")}}function Xt(i,t){if(i&1&&(C(0),n(1,"div",20)(2,"table",21,0),C(4,22),p(5,Bt,2,0,"th",23)(6,Ut,2,1,"td",24),x(),C(7,25),p(8,Nt,2,0,"th",23)(9,jt,2,1,"td",24),x(),C(10,26),p(11,$t,2,0,"th",23)(12,qt,3,4,"td",24),x(),C(13,27),p(14,zt,2,0,"th",23)(15,Ht,3,4,"td",24),x(),C(16,28),p(17,Lt,2,0,"th",29)(18,Gt,3,0,"td",24),x(),p(19,Qt,1,0,"tr",30)(20,Yt,1,0,"tr",31),o(),h(21,"mat-paginator",32,1),o(),n(23,"div",33)(24,"mat-accordion",34),p(25,Wt,20,10,"mat-expansion-panel",35),o()(),x()),i&2){let e=m();l(2),c("dataSource",e.availableDataSource),l(17),c("matHeaderRowDef",e.availableColumns),l(),c("matRowDefColumns",e.availableColumns),l(),c("pageSizeOptions",re(6,Dt))("pageSize",3),l(4),c("ngForOf",e.availableRides())}}function Zt(i,t){if(i&1){let e=v();n(0,"div",43)(1,"div",44)(2,"button",45),u("click",function(){_(e);let s=m();return g(s.statusFilter.set("all"))}),r(3,"All"),o(),n(4,"button",45),u("click",function(){_(e);let s=m();return g(s.statusFilter.set("assigned"))}),r(5,"Assigned"),o(),n(6,"button",45),u("click",function(){_(e);let s=m();return g(s.statusFilter.set("in-progress"))}),r(7,"In Progress"),o(),n(8,"button",45),u("click",function(){_(e);let s=m();return g(s.statusFilter.set("completed"))}),r(9,"Completed"),o()()()}if(i&2){let e=m();l(2),c("color",e.statusFilter()==="all"?"primary":""),l(2),c("color",e.statusFilter()==="assigned"?"primary":""),l(2),c("color",e.statusFilter()==="in-progress"?"primary":""),l(2),c("color",e.statusFilter()==="completed"?"primary":"")}}function Jt(i,t){i&1&&(n(0,"div",17),h(1,"mat-spinner",18),n(2,"p"),r(3,"Loading rides..."),o()())}function Kt(i,t){i&1&&(n(0,"div",19)(1,"p"),r(2,"You don't have any assigned rides."),o()())}function ei(i,t){i&1&&(n(0,"div",19)(1,"p"),r(2,"No rides match the current filter."),o()())}function ti(i,t){i&1&&(n(0,"th",36),r(1,"Pickup"),o())}function ii(i,t){if(i&1&&(n(0,"td",37),r(1),o()),i&2){let e=t.$implicit;l(),S(e.pickup_location)}}function ni(i,t){i&1&&(n(0,"th",36),r(1,"Destination"),o())}function oi(i,t){if(i&1&&(n(0,"td",37),r(1),o()),i&2){let e=t.$implicit;l(),S(e.dropoff_location)}}function ri(i,t){i&1&&(n(0,"th",36),r(1,"Time"),o())}function ai(i,t){if(i&1&&(n(0,"td",37),r(1),R(2,"date"),o()),i&2){let e=t.$implicit;l(),S(w(2,1,e.pickup_time,"short"))}}function si(i,t){i&1&&(n(0,"th",36),r(1,"Status"),o())}function li(i,t){if(i&1&&(n(0,"td",37)(1,"span",48),r(2),o()()),i&2){let e=t.$implicit;l(),c("ngClass","status-"+e.status),l(),y(" ",e.status," ")}}function ci(i,t){i&1&&(n(0,"th",36),r(1,"Fare"),o())}function di(i,t){if(i&1&&(n(0,"td",37),r(1),R(2,"currency"),o()),i&2){let e=t.$implicit;l(),S(e.fare?w(2,1,e.fare*.7,"USD"):"TBD")}}function mi(i,t){i&1&&(n(0,"th",38),r(1,"Actions"),o())}function pi(i,t){if(i&1){let e=v();n(0,"button",39),u("click",function(){_(e);let s=m().$implicit,d=m(2);return g(d.startRide(s.id))}),r(1," Start Ride "),o()}}function ui(i,t){if(i&1){let e=v();n(0,"button",54),u("click",function(){_(e);let s=m().$implicit,d=m(2);return g(d.cancelAssignment(s.id))}),r(1," Cancel Assignment "),o()}}function _i(i,t){if(i&1){let e=v();n(0,"button",55),u("click",function(){_(e);let s=m().$implicit,d=m(2);return g(d.completeRide(s.id))}),r(1," Complete "),o()}}function gi(i,t){if(i&1){let e=v();n(0,"button",56),u("click",function(){_(e);let s=m().$implicit,d=m(2);return g(d.showNavigation(s))}),n(1,"mat-icon"),r(2,"navigation"),o()()}}function fi(i,t){if(i&1){let e=v();n(0,"td",37),p(1,pi,2,0,"button",49)(2,ui,2,0,"button",50)(3,_i,2,0,"button",51)(4,gi,3,0,"button",52),n(5,"button",53),u("click",function(){let s=_(e).$implicit,d=m(2);return g(d.viewRideDetails(s.id))}),n(6,"mat-icon"),r(7,"visibility"),o()()()}if(i&2){let e=t.$implicit;l(),c("ngIf",e.status==="assigned"),l(),c("ngIf",e.status==="assigned"),l(),c("ngIf",e.status==="in-progress"),l(),c("ngIf",e.status!=="completed")}}function vi(i,t){i&1&&h(0,"tr",40)}function hi(i,t){i&1&&h(0,"tr",41)}function Ci(i,t){if(i&1){let e=v();n(0,"button",62),u("click",function(s){_(e);let d=m().$implicit;return m(2).startRide(d.id),g(s.stopPropagation())}),n(1,"mat-icon"),r(2,"play_arrow"),o()()}}function xi(i,t){if(i&1){let e=v();n(0,"button",63),u("click",function(s){_(e);let d=m().$implicit;return m(2).completeRide(d.id),g(s.stopPropagation())}),n(1,"mat-icon"),r(2,"check_circle"),o()()}}function bi(i,t){if(i&1){let e=v();n(0,"button",64),u("click",function(){_(e);let s=m().$implicit,d=m(2);return g(d.cancelAssignment(s.id))}),r(1," Cancel Assignment "),o()}}function Si(i,t){if(i&1){let e=v();n(0,"button",65),u("click",function(){_(e);let s=m().$implicit,d=m(2);return g(d.showNavigation(s))}),n(1,"mat-icon"),r(2,"navigation"),o()()}}function yi(i,t){if(i&1){let e=v();n(0,"mat-expansion-panel")(1,"mat-expansion-panel-header")(2,"mat-panel-description"),r(3),p(4,Ci,3,0,"button",57)(5,xi,3,0,"button",58),o(),n(6,"mat-panel-title")(7,"div",59)(8,"span",48),r(9),o()()()(),n(10,"div",42)(11,"p")(12,"strong"),r(13,"To:"),o(),r(14),o(),n(15,"p")(16,"strong"),r(17,"Time:"),o(),r(18),R(19,"date"),o(),n(20,"p")(21,"strong"),r(22,"Fare:"),o(),r(23),R(24,"currency"),o()(),n(25,"mat-action-row"),p(26,bi,2,0,"button",60)(27,Si,3,0,"button",61),n(28,"button",53),u("click",function(){let s=_(e).$implicit,d=m(2);return g(d.viewRideDetails(s.id))}),n(29,"mat-icon"),r(30,"visibility"),o()()()()}if(i&2){let e=t.$implicit;l(3),y(" ",e.pickup_location," "),l(),c("ngIf",e.status==="assigned"),l(),c("ngIf",e.status==="in-progress"),l(3),c("ngClass","status-"+e.status),l(),S(e.status),l(5),y(" ",e.dropoff_location,""),l(4),y(" ",w(19,10,e.pickup_time,"short"),""),l(5),y(" ",e.fare?w(24,13,e.fare*.7,"USD"):"TBD",""),l(3),c("ngIf",e.status==="assigned"),l(),c("ngIf",e.status!=="completed")}}function Mi(i,t){if(i&1&&(C(0),n(1,"div",20)(2,"table",21,2),C(4,22),p(5,ti,2,0,"th",23)(6,ii,2,1,"td",24),x(),C(7,25),p(8,ni,2,0,"th",23)(9,oi,2,1,"td",24),x(),C(10,26),p(11,ri,2,0,"th",23)(12,ai,3,4,"td",24),x(),C(13,46),p(14,si,2,0,"th",23)(15,li,3,2,"td",24),x(),C(16,27),p(17,ci,2,0,"th",23)(18,di,3,4,"td",24),x(),C(19,28),p(20,mi,2,0,"th",29)(21,fi,8,4,"td",24),x(),p(22,vi,1,0,"tr",30)(23,hi,1,0,"tr",31),o(),h(24,"mat-paginator",47,3),o(),n(26,"div",33)(27,"mat-accordion",34),p(28,yi,31,16,"mat-expansion-panel",35),o()(),x()),i&2){let e=m();l(2),c("dataSource",e.myRidesDataSource),l(20),c("matHeaderRowDef",e.myRidesColumns),l(),c("matRowDefColumns",e.myRidesColumns),l(),c("pageSizeOptions",re(6,Ot))("pageSize",5),l(4),c("ngForOf",e.filteredMyRides())}}function Ri(i,t){if(i&1){let e=v();n(0,"app-ride-navigation",66),u("close",function(){_(e);let s=m();return g(s.selectedRide=null)}),o()}if(i&2){let e=m();c("ride",e.selectedRide)}}function wi(i,t){if(i&1){let e=v();n(0,"div",67),u("click",function(){_(e);let s=m();return g(s.closeRideDetails())}),n(1,"app-ride-detail",68),u("rideUpdated",function(s){_(e);let d=m();return g(d.onRideUpdated(s))})("click",function(s){return _(e),g(s.stopPropagation())}),o()()}if(i&2){let e=m();l(),c("rideId",e.selectedRideId)("onClose",e.closeRideDetails.bind(e))}}var ne=class i{constructor(t,e,a,s,d){this.rideService=t;this.authService=e;this.messageService=a;this.router=s;this.snackBar=d;_e(()=>{let H=this.statusFilter(),le=this.myRides();H==="all"?this.myRidesDataSource.data=le:this.myRidesDataSource.data=le.filter(xt=>xt.status===H)})}currentUser=null;selectedRide=null;selectedRideId=null;loading=!1;ridesSubscription=null;availableRides=L([]);myRides=L([]);statusFilter=L("all");filteredMyRides=ue(()=>{let t=this.myRides(),e=this.statusFilter();return e==="all"?t:t.filter(a=>a.status===e)});availableColumns=["pickup_location","dropoff_location","pickup_time","fare","actions"];myRidesColumns=["pickup_location","dropoff_location","pickup_time","status","fare","actions"];availableDataSource=new ae([]);myRidesDataSource=new ae([]);availableSort;availablePaginator;myRidesSort;myRidesPaginator;ngOnInit(){return f(this,null,function*(){try{yield this.loadCurrentUser(),this.currentUser&&(this.setupRealtimeSubscription(),yield this.loadRides(),console.log("\u2705 Driver dashboard initialized with realtime updates"))}catch(t){console.error("\u274C Error loading current user:",t),this.snackBar.open("Failed to load user information","Close",{duration:3e3})}})}ngAfterViewInit(){setTimeout(()=>{this.setupDataSources()},0)}ngOnDestroy(){this.ridesSubscription&&this.ridesSubscription.unsubscribe()}loadCurrentUser(){return f(this,null,function*(){try{this.loading=!0,this.currentUser=yield this.authService.getCurrentUser()}catch(t){console.error("Error loading current user:",t),this.snackBar.open("Failed to load user information","Close",{duration:3e3})}finally{this.loading=!1}})}setupRealtimeSubscription(){this.currentUser&&(console.log("\u{1F504} Setting up realtime subscription for driver:",this.currentUser.id),this.ridesSubscription=this.rideService.rides$.subscribe(t=>{if(t&&t.length>=0){let e=t.filter(d=>d.status==="requested"),a=t.filter(d=>d.driver_id===this.currentUser?.id&&["assigned","in-progress","completed"].includes(d.status));this.availableRides.set(e),this.myRides.set(a),this.availableDataSource.data=e;let s=this.availableDataSource.data.length;e.length>s&&s>0&&this.snackBar.open(`${e.length-s} new ride(s) available!`,"View",{duration:5e3,panelClass:["success-snackbar"]})}}))}setupDataSources(){console.log("Setting up data sources..."),this.availableSort&&(this.availableDataSource.sort=this.availableSort),this.availablePaginator&&(this.availableDataSource.paginator=this.availablePaginator),this.myRidesSort&&(this.myRidesDataSource.sort=this.myRidesSort),this.myRidesPaginator&&(this.myRidesDataSource.paginator=this.myRidesPaginator),this.availableDataSource.sortingDataAccessor=(t,e)=>{switch(e){case"pickup_time":return t[e]?new Date(t[e]).getTime():0;case"fare":return t.fare||0;case"pickup_location":case"dropoff_location":return t[e]?.toLowerCase()||"";default:return t[e]||""}},this.myRidesDataSource.sortingDataAccessor=(t,e)=>{switch(e){case"pickup_time":return t[e]?new Date(t[e]).getTime():0;case"fare":return t.fare||0;case"pickup_location":case"dropoff_location":case"status":return t[e]?.toLowerCase()||"";default:return t[e]||""}}}loadRides(){return f(this,null,function*(){if(this.currentUser){console.log("\u{1F504} Loading rides..."),this.loading=!0;try{yield this.rideService.getAllRides();let[t,e]=yield Promise.all([this.rideService.getAvailableRides(),this.rideService.getDriverRides(this.currentUser.id)]);this.availableRides.set(t),this.myRides.set(e),this.availableDataSource.data=t,setTimeout(()=>{this.setupDataSources()},0),this.snackBar.open("Rides refreshed","Close",{duration:2e3})}catch(t){console.error("\u274C Error loading rides:",t),this.snackBar.open(t.message||"Failed to load rides","Close",{duration:3e3})}finally{this.loading=!1}}})}acceptRide(t){return f(this,null,function*(){if(this.currentUser)try{yield this.rideService.acceptRide(t,this.currentUser.id),this.snackBar.open("Ride accepted successfully","Close",{duration:3e3})}catch(e){console.error("Error accepting ride:",e),this.snackBar.open(e.message||"Failed to accept ride","Close",{duration:3e3})}})}startRide(t){return f(this,null,function*(){try{yield this.rideService.startRide(t),this.snackBar.open("Ride started successfully","Close",{duration:3e3})}catch(e){console.error("Error starting ride:",e),this.snackBar.open(e.message||"Failed to start ride","Close",{duration:3e3})}})}completeRide(t){return f(this,null,function*(){try{yield this.rideService.completeRide(t),this.snackBar.open("Ride completed successfully","Close",{duration:3e3})}catch(e){console.error("Error completing ride:",e),this.snackBar.open(e.message||"Failed to complete ride","Close",{duration:3e3})}})}cancelAssignment(t){return f(this,null,function*(){try{yield this.rideService.cancelDriverAssignment(t),this.snackBar.open("Assignment cancelled successfully. Ride returned to available requests.","Close",{duration:4e3})}catch(e){console.error("Error cancelling assignment:",e),this.snackBar.open(e.message||"Failed to cancel assignment","Close",{duration:3e3})}})}showNavigation(t){this.selectedRide=t}closeNavigation(){this.selectedRide=null}openChat(t){return f(this,null,function*(){try{let e=yield this.messageService.getOrCreateThreadForRide(t);yield this.router.navigate(["/dashboard","driver","messages",e.id])}catch(e){console.error("Error opening chat:",e),this.snackBar.open(e.message||"Failed to open chat","Close",{duration:3e3})}})}viewRideDetails(t){this.selectedRideId=t}closeRideDetails(){this.selectedRideId=null}onRideUpdated(t){}trackByRideId(t,e){return e.id}static \u0275fac=function(e){return new(e||i)(b(Fe),b(A),b(je),b(Ce),b(Q))};static \u0275cmp=k({type:i,selectors:[["app-ride-assignments"]],viewQuery:function(e,a){if(e&1&&($(Et,5),$(It,5),$(Vt,5),$(Tt,5)),e&2){let s;q(s=z())&&(a.availableSort=s.first),q(s=z())&&(a.availablePaginator=s.first),q(s=z())&&(a.myRidesSort=s.first),q(s=z())&&(a.myRidesPaginator=s.first)}},decls:32,vars:13,consts:[["availableSort","matSort"],["availablePaginator",""],["myRidesSort","matSort"],["myRidesPaginator",""],[1,"assignments-container"],["label","Available Rides",3,"tabIndex"],[1,"table-container"],[1,"header-with-actions"],["title","Realtime updates active",1,"realtime-indicator"],["mat-icon-button","","color","primary","matTooltip","Manual refresh",3,"click"],["class","loading-container",4,"ngIf"],["class","no-rides",4,"ngIf"],[4,"ngIf"],["label","My Rides",3,"tabIndex","disabled"],["class","filter-container",4,"ngIf"],[3,"ride","close",4,"ngIf"],["class","ride-detail-overlay",3,"click",4,"ngIf"],[1,"loading-container"],["diameter","40"],[1,"no-rides"],[1,"desktop-view"],["mat-table","","matSort","",1,"ride-table",3,"dataSource"],["matColumnDef","pickup_location"],["mat-header-cell","","mat-sort-header","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","dropoff_location"],["matColumnDef","pickup_time"],["matColumnDef","fare"],["matColumnDef","actions"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["showFirstLastButtons","","aria-label","Select page of available rides",3,"pageSizeOptions","pageSize"],[1,"mobile-view"],["multi",""],[4,"ngFor","ngForOf"],["mat-header-cell","","mat-sort-header",""],["mat-cell",""],["mat-header-cell",""],["mat-raised-button","","color","primary",3,"click"],["mat-header-row",""],["mat-row",""],[1,"ride-details"],[1,"filter-container"],["aria-label","Filter rides by status",1,"filter-buttons"],["mat-stroked-button","",1,"filter-button",3,"click","color"],["matColumnDef","status"],["showFirstLastButtons","","aria-label","Select page of my rides",3,"pageSizeOptions","pageSize"],[1,"status-chip",3,"ngClass"],["mat-raised-button","","color","primary",3,"click",4,"ngIf"],["mat-raised-button","","color","warn","style","margin-left: 8px;",3,"click",4,"ngIf"],["mat-raised-button","","color","accent",3,"click",4,"ngIf"],["mat-icon-button","","color","primary",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","View Details",3,"click"],["mat-raised-button","","color","warn",2,"margin-left","8px",3,"click"],["mat-raised-button","","color","accent",3,"click"],["mat-icon-button","","color","primary",3,"click"],["mat-icon-button","","color","primary","matTooltip","Start Ride",3,"click",4,"ngIf"],["mat-icon-button","","color","accent","matTooltip","Complete Ride",3,"click",4,"ngIf"],[1,"ride-actions-header"],["mat-raised-button","","color","warn","style","margin-right: 8px;",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","Navigation",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","Start Ride",3,"click"],["mat-icon-button","","color","accent","matTooltip","Complete Ride",3,"click"],["mat-raised-button","","color","warn",2,"margin-right","8px",3,"click"],["mat-icon-button","","color","primary","matTooltip","Navigation",3,"click"],[3,"close","ride"],[1,"ride-detail-overlay",3,"click"],[3,"rideUpdated","click","rideId","onClose"]],template:function(e,a){e&1&&(n(0,"div",4)(1,"mat-tab-group")(2,"mat-tab",5)(3,"div",6)(4,"div",7)(5,"h3"),r(6,"Available Ride Requests "),n(7,"span",8),r(8,"\u25CF"),o()(),n(9,"button",9),u("click",function(){return a.loadRides()}),n(10,"mat-icon"),r(11,"refresh"),o()()(),p(12,At,4,0,"div",10)(13,Ft,3,0,"div",11)(14,Xt,26,7,"ng-container",12),o()(),n(15,"mat-tab",13)(16,"div",6)(17,"div",7)(18,"h3"),r(19,"My Assigned Rides "),n(20,"span",8),r(21,"\u25CF"),o()(),n(22,"button",9),u("click",function(){return a.loadRides()}),n(23,"mat-icon"),r(24,"refresh"),o()()(),p(25,Zt,10,4,"div",14)(26,Jt,4,0,"div",10)(27,Kt,3,0,"div",11)(28,ei,3,0,"div",11)(29,Mi,29,7,"ng-container",12),o()()(),p(30,Ri,1,1,"app-ride-navigation",15)(31,wi,2,2,"div",16),o()),e&2&&(l(2),c("tabIndex",0),l(10),c("ngIf",a.loading),l(),c("ngIf",!a.loading&&a.availableRides().length===0),l(),c("ngIf",!a.loading&&a.availableRides().length>0),l(),c("tabIndex",1)("disabled",a.myRides().length===0),l(10),c("ngIf",a.myRides().length>0),l(),c("ngIf",a.loading),l(),c("ngIf",!a.loading&&a.myRides().length===0),l(),c("ngIf",!a.loading&&a.filteredMyRides().length===0&&a.myRides().length>0),l(),c("ngIf",!a.loading&&a.filteredMyRides().length>0),l(),c("ngIf",a.selectedRide),l(),c("ngIf",a.selectedRideId))},dependencies:[P,ge,fe,V,he,ve,E,J,X,Z,Ke,ze,Le,We,Ge,He,Xe,Qe,Ye,Ze,Je,qe,D,T,G,O,W,Y,ct,tt,et,Ue,Be,ie,$e,pt,dt,mt,_t,ut,lt,st,it,nt,ot,at,rt],styles:[".mat-expansion-panel-header-description[_ngcontent-%COMP%]{flex-grow:4!important}.assignments-container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto}.table-container[_ngcontent-%COMP%]{margin:20px}.ride-table[_ngcontent-%COMP%]{width:100%}.no-rides[_ngcontent-%COMP%]{padding:20px;text-align:center;color:#666;font-style:italic}.status-chip[_ngcontent-%COMP%]{border-radius:16px;padding:4px 12px;color:#fff;font-weight:500}.status-requested[_ngcontent-%COMP%]{background-color:#ff9800}.status-assigned[_ngcontent-%COMP%]{background-color:#2196f3}.status-in-progress[_ngcontent-%COMP%]{background-color:#673ab7}.status-completed[_ngcontent-%COMP%]{background-color:#4caf50}.status-canceled[_ngcontent-%COMP%]{background-color:#f44336}.header-with-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px;color:#666}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:10px}.ride-detail-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.realtime-indicator[_ngcontent-%COMP%]{color:#4caf50;font-size:12px;animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}.desktop-view[_ngcontent-%COMP%]{display:block}.mobile-view[_ngcontent-%COMP%]{display:none}.filter-container[_ngcontent-%COMP%]{margin:0 20px 20px}.ride-actions-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}@media (max-width: 600px){.desktop-view[_ngcontent-%COMP%]{display:none}.mobile-view[_ngcontent-%COMP%]{display:block}.assignments-container[_ngcontent-%COMP%]{padding:0}.table-container[_ngcontent-%COMP%]{margin:0}.mat-tab-body-content[_ngcontent-%COMP%]{overflow:hidden}.filter-container[_ngcontent-%COMP%]{margin:0 0 16px}}.mobile-view[_ngcontent-%COMP%]   .mat-expansion-panel[_ngcontent-%COMP%]{margin:8px 0}.mobile-view[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{font-size:12px}.mobile-view[_ngcontent-%COMP%]   .mat-panel-title[_ngcontent-%COMP%]{font-weight:500}.mobile-view[_ngcontent-%COMP%]   .mat-panel-description[_ngcontent-%COMP%]{justify-content:flex-end;align-items:center}.filter-button[_ngcontent-%COMP%]{margin-bottom:8px}.mobile-view[_ngcontent-%COMP%]   .ride-details[_ngcontent-%COMP%]{padding:0 24px 16px;font-size:12px}.mobile-view[_ngcontent-%COMP%]   .ride-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0}.mobile-view[_ngcontent-%COMP%]   .mat-action-row[_ngcontent-%COMP%]{justify-content:flex-end;padding:8px 12px 8px 24px}"]})};var Ct=class i{static \u0275fac=function(e){return new(e||i)};static \u0275cmp=k({type:i,selectors:[["app-driver"]],decls:13,vars:0,consts:[[1,"dashboard-container"],[1,"welcome-card"],["label","Ride Assignments"],["label","Driver Profile"]],template:function(e,a){e&1&&(n(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title"),r(4,"Driver Dashboard"),o()(),n(5,"mat-card-content")(6,"p"),r(7,"Welcome to your driver dashboard! Here you can manage your profile, vehicle information, and ride assignments."),o()()(),n(8,"mat-tab-group")(9,"mat-tab",2),h(10,"app-ride-assignments"),o(),n(11,"mat-tab",3),h(12,"app-driver-profile"),o()()())},dependencies:[P,E,F,U,N,B,J,X,Z,te,ne],styles:[".dashboard-container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto;background-color:#f5f5f5}.welcome-card[_ngcontent-%COMP%]{margin-bottom:20px}ul[_ngcontent-%COMP%]{list-style-type:none;padding:0;margin:16px 0}li[_ngcontent-%COMP%]{padding:8px 0}"]})};export{Ct as DriverComponent};

import{a as Fe,b as qe,c as Ve,d as Be,e as Le}from"./chunk-OCX3SM77.js";import{A as De,j as pe,o as Q,v as ke,w as Ee,x as Ie,y as Te,z as Oe}from"./chunk-KJHHDGHU.js";import"./chunk-MS4AQ6UA.js";import{A as ft,B as _t,C as gt,D as Ct,E as xt,b as A,c as He,d as Ge,e as Qe,g as We,h as Je,i as Xe,j as Ye,k as Ze,l as et,m as tt,n as it,o as nt,p as rt,q as ot,r as at,s as st,t as ct,u as mt,w as dt,x as lt,y as pt,z as ut}from"./chunk-HEB4POL5.js";import{a as B,b as Ue,c as $e,d as je,e as ze}from"./chunk-YSBVITGM.js";import{a as Ae,b as Ne}from"./chunk-OFTCLERB.js";import{a as Ke}from"./chunk-EACHO2FA.js";import"./chunk-ORDMVBIZ.js";import{a as D,b as F}from"./chunk-WSXVBUWR.js";import"./chunk-IT7B5FSQ.js";import"./chunk-3MEMYPGR.js";import"./chunk-EO3PEVGH.js";import{a as O,b as G}from"./chunk-553Y2ERR.js";import"./chunk-3VEHVC57.js";import"./chunk-Q34CP4BD.js";import{c as le}from"./chunk-3EEDYH74.js";import{C as Re,F as be,H as Pe,I as we,J as q,b as ue,d as j,f as fe,g as _e,k as ge,n as Ce,o as xe,s as ve,u as ye,w as he,x as Se,z as Me}from"./chunk-AG3SD6JT.js";import{Bc as z,Bd as L,Eb as ae,Fb as se,Fc as I,Gb as ce,Gd as H,Jb as te,Jd as T,Kb as o,Kd as ie,La as a,Lb as h,Ld as K,Mb as _,Md as W,Nd as J,Pa as u,Qd as X,Rd as V,Wa as w,Wb as U,Yb as $,ab as p,hb as m,kb as ee,la as g,ma as C,qb as r,rb as n,sb as x,tb as b,tc as me,ub as k,uc as de,va as oe,vc as E,wb as S,yb as v,zb as d}from"./chunk-ST4QC4E3.js";import"./chunk-X5YLR3NI.js";import{a as ne,b as re,i as y}from"./chunk-ODN5LVDJ.js";function It(i,t){i&1&&(r(0,"mat-error"),o(1," Pickup location is required "),n())}function Tt(i,t){i&1&&(r(0,"mat-error"),o(1," Dropoff location is required "),n())}function Ot(i,t){if(i&1&&(r(0,"p"),o(1),n()),i&2){let e=d(3);a(),_("Distance: ",e.estimatedDistance," miles")}}function Dt(i,t){if(i&1&&(r(0,"p"),o(1),n()),i&2){let e=d(3);a(),_("Duration: ",e.estimatedDuration," minutes")}}function Ft(i,t){if(i&1&&(r(0,"div",17)(1,"p"),o(2,"Estimated fare: "),r(3,"strong"),o(4),n()(),p(5,Ot,2,1,"p",7)(6,Dt,2,1,"p",7),n()),i&2){let e=d(2);a(4),h(e.estimatedFare?"$"+e.estimatedFare.toFixed(2):""),a(),m("ngIf",e.estimatedDistance),a(),m("ngIf",e.estimatedDuration)}}function qt(i,t){if(i&1&&(r(0,"div"),x(1,"app-map-display",15),p(2,Ft,7,3,"div",16),n()),i&2){let e,s,c=d();a(),m("origin",(e=c.rideForm.get("pickup_location"))==null?null:e.value)("destination",(s=c.rideForm.get("dropoff_location"))==null?null:s.value),a(),m("ngIf",c.estimatedFare)}}function Vt(i,t){i&1&&(r(0,"mat-error"),o(1," Pickup date is required "),n())}function Bt(i,t){i&1&&(r(0,"mat-error"),o(1," Pickup time is required "),n())}var Y=class i{constructor(t,e,s,c,l,f){this.formBuilder=t;this.rideService=e;this.authService=s;this.locationService=c;this.paymentService=l;this.snackBar=f;this.rideForm=this.formBuilder.group({pickup_location:["",j.required],dropoff_location:["",j.required],pickup_date:[new Date,j.required],pickup_time:["12:00 PM",j.required]})}rideForm;loading=!1;showMap=!1;estimatedFare=null;estimatedDistance=null;estimatedDuration=null;locationCoordinates={};ngOnInit(){this.rideForm.get("pickup_location")?.valueChanges.subscribe(()=>{this.updateRouteEstimates()}),this.rideForm.get("dropoff_location")?.valueChanges.subscribe(()=>{this.updateRouteEstimates()})}useCurrentLocation(){return y(this,null,function*(){try{let t=yield this.locationService.getCurrentLocation();this.rideForm.patchValue({pickup_location:`Current Location (${t.latitude.toFixed(6)}, ${t.longitude.toFixed(6)})`}),this.locationCoordinates.pickup=t,this.snackBar.open("Current location detected","Close",{duration:2e3})}catch(t){this.snackBar.open(t.message||"Failed to get current location","Close",{duration:3e3})}})}updateRouteEstimates(){return y(this,null,function*(){let t=this.rideForm.get("pickup_location")?.value,e=this.rideForm.get("dropoff_location")?.value;if(t&&e){this.showMap=!0;try{let{fare:s,routeInfo:c}=yield this.paymentService.estimateFare(t,e);this.estimatedFare=s,this.estimatedDistance=c.distance,this.estimatedDuration=c.duration}catch(s){console.error("Error calculating route:",s)}}else this.showMap=!1,this.estimatedFare=null,this.estimatedDistance=null,this.estimatedDuration=null})}onSubmit(){return y(this,null,function*(){if(!this.rideForm.invalid){this.loading=!0;try{let t=yield this.authService.getCurrentUser();if(!t)throw new Error("User not found");this.locationCoordinates.pickup||(this.locationCoordinates.pickup=yield this.locationService.geocodeAddress(this.rideForm.value.pickup_location)),this.locationCoordinates.dropoff||(this.locationCoordinates.dropoff=yield this.locationService.geocodeAddress(this.rideForm.value.dropoff_location));let e=yield this.locationService.calculateRoute(this.locationCoordinates.pickup,this.locationCoordinates.dropoff);console.log(e);let s=this.rideForm.value.pickup_date,c=this.rideForm.value.pickup_time,l=new Date(s),f=c.match(/(\d+):(\d+)\s?(AM|PM)?/i);if(f){let M=parseInt(f[1],10),P=parseInt(f[2],10),N=f[3]?f[3].toUpperCase():null;N==="PM"&&M<12?M+=12:N==="AM"&&M===12&&(M=0),l.setHours(M,P,0,0)}let R=re(ne({},this.rideForm.value),{rider_id:t.id,status:"requested",pickup_time:l.toISOString(),pickup_latitude:this.locationCoordinates.pickup?.latitude,pickup_longitude:this.locationCoordinates.pickup?.longitude,dropoff_latitude:this.locationCoordinates.dropoff?.latitude,dropoff_longitude:this.locationCoordinates.dropoff?.longitude,distance_miles:e.distance,duration_minutes:e.duration,fare:this.estimatedFare||(yield this.paymentService.estimateFare(this.rideForm.value.pickup_location,this.rideForm.value.dropoff_location))});yield this.rideService.createRide(R),this.snackBar.open("Ride requested successfully!","Close",{duration:3e3}),this.rideForm.reset({passengers:1,pickup_date:new Date,pickup_time:"12:00 PM"}),this.showMap=!1,this.estimatedFare=null,this.estimatedDistance=null,this.estimatedDuration=null,this.locationCoordinates={}}catch(t){this.snackBar.open(t.message||"Failed to request ride","Close",{duration:3e3})}finally{this.loading=!1}}})}static \u0275fac=function(e){return new(e||i)(u(ve),u(B),u(q),u(Ae),u(A),u(O))};static \u0275cmp=w({type:i,selectors:[["app-ride-request"]],decls:42,vars:12,consts:[["picker",""],["timepicker",""],[3,"ngSubmit","formGroup"],[1,"location-fields"],["appearance","outline"],["matInput","","formControlName","pickup_location","placeholder","Enter pickup location"],["mat-icon-button","","matSuffix","","type","button","title","Use current location",3,"click"],[4,"ngIf"],["matInput","","formControlName","dropoff_location","placeholder","Enter dropoff location"],["matInput","","formControlName","pickup_date",3,"matDatepicker"],["matSuffix","",3,"for"],["matInput","","formControlName","pickup_time",3,"ngxMatTimepicker"],["ngxMatTimepickerToggleIcon",""],[1,"button-container"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[3,"origin","destination"],["class","fare-estimate",4,"ngIf"],[1,"fare-estimate"]],template:function(e,s){if(e&1){let c=S();r(0,"mat-card")(1,"mat-card-header")(2,"mat-card-title"),o(3,"Request a Ride"),n()(),r(4,"mat-card-content")(5,"form",2),v("ngSubmit",function(){return g(c),C(s.onSubmit())}),r(6,"div",3)(7,"mat-form-field",4)(8,"mat-label"),o(9,"Pickup Location"),n(),x(10,"input",5),r(11,"button",6),v("click",function(){return g(c),C(s.useCurrentLocation())}),r(12,"mat-icon"),o(13,"my_location"),n()(),p(14,It,2,0,"mat-error",7),n(),r(15,"mat-form-field",4)(16,"mat-label"),o(17,"Dropoff Location"),n(),x(18,"input",8),p(19,Tt,2,0,"mat-error",7),n()(),p(20,qt,3,3,"div",7),r(21,"mat-form-field",4)(22,"mat-label"),o(23,"Pickup Date"),n(),x(24,"input",9)(25,"mat-datepicker-toggle",10)(26,"mat-datepicker",null,0),p(28,Vt,2,0,"mat-error",7),n(),r(29,"mat-form-field",4)(30,"mat-label"),o(31,"Pick a time"),n(),x(32,"input",11),r(33,"ngx-mat-timepicker-toggle",10)(34,"mat-icon",12),o(35,"keyboard_arrow_down"),n()(),x(36,"ngx-mat-timepicker",null,1),p(38,Bt,2,0,"mat-error",7),n(),r(39,"div",13)(40,"button",14),o(41),n()()()()()}if(e&2){let c,l,f,R,M,P=te(27),N=te(37);a(5),m("formGroup",s.rideForm),a(9),m("ngIf",(c=s.rideForm.get("pickup_location"))==null||c.errors==null?null:c.errors.required),a(5),m("ngIf",(l=s.rideForm.get("dropoff_location"))==null||l.errors==null?null:l.errors.required),a(),m("ngIf",s.showMap&&((f=s.rideForm.get("pickup_location"))==null?null:f.value)&&((f=s.rideForm.get("dropoff_location"))==null?null:f.value)),a(4),m("matDatepicker",P),a(),m("for",P),a(3),m("ngIf",(R=s.rideForm.get("pickup_date"))==null||R.errors==null?null:R.errors.required),a(4),m("ngxMatTimepicker",N),a(),m("for",N),a(5),m("ngIf",(M=s.rideForm.get("pickup_time"))==null||M.errors==null?null:M.errors.required),a(2),m("disabled",s.rideForm.invalid||s.loading),a(),_(" ",s.loading?"Requesting...":"Request Ride"," ")}},dependencies:[I,E,ye,ge,ue,fe,_e,Ce,xe,V,K,J,X,W,be,Re,he,Se,Me,we,Pe,T,H,L,Be,Fe,qe,Ve,ke,F,D,Ne,De,Ee,Te,Oe,Ie],styles:["[_nghost-%COMP%]{display:block;margin:20px}form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;max-width:600px;margin:0 auto}.location-fields[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.button-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:16px}textarea[_ngcontent-%COMP%]{min-height:100px}.fare-estimate[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:16px;border-radius:4px;margin-top:16px;margin-bottom:16px}.fare-estimate[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0}"],changeDetection:0})};var At=["cardElement"];function Nt(i,t){i&1&&x(0,"mat-divider",13)}function Ut(i,t){i&1&&(r(0,"div",14)(1,"p"),o(2,"Loading payment form..."),n(),x(3,"mat-spinner",15),n())}function $t(i,t){if(i&1&&(r(0,"div",23),o(1),n()),i&2){let e=d(3);a(),h(e.cardError)}}function jt(i,t){i&1&&(r(0,"div",24),x(1,"mat-spinner",25),r(2,"span"),o(3,"Processing your payment..."),n()())}function zt(i,t){if(i&1){let e=S();r(0,"div",16)(1,"h3"),o(2,"Payment Information"),n(),r(3,"p",17),o(4,"Please enter your card details to complete the payment."),n(),x(5,"div",18,0),p(7,$t,2,1,"div",19),r(8,"div",20)(9,"button",21),v("click",function(){g(e);let c=d(2);return C(c.processPayment())}),r(10,"mat-icon"),o(11,"payment"),n(),o(12),n(),p(13,jt,4,0,"div",22),n()()}if(i&2){let e=d(2);a(7),m("ngIf",e.cardError),a(2),m("disabled",e.processing||!e.cardComplete),a(3),_(" ",e.processing?"Processing...":"Pay Now"," "),a(),m("ngIf",e.processing)}}function Lt(i,t){i&1&&(r(0,"div",24),x(1,"mat-spinner",25),r(2,"span"),o(3,"Processing your request..."),n()())}function Ht(i,t){if(i&1){let e=S();r(0,"div",20)(1,"button",26),v("click",function(){g(e);let c=d(2);return C(c.requestRefund())}),r(2,"mat-icon"),o(3,"money_off"),n(),o(4),n(),p(5,Lt,4,0,"div",22),n()}if(i&2){let e=d(2);a(),m("disabled",e.processing),a(3),_(" ",e.processing?"Processing...":"Request Refund"," "),a(),m("ngIf",e.processing)}}function Gt(i,t){if(i&1&&(r(0,"div",27)(1,"h3"),o(2,"Payment Result"),n(),r(3,"div",28),o(4),n()()),i&2){let e=d(2);a(3),m("ngClass",e.paymentResult.success?"success-message":"error-message"),a(),_(" ",e.paymentResult.success?"Payment successful!":"Payment failed: "+(e.paymentResult.error==null?null:e.paymentResult.error.message)," ")}}function Qt(i,t){if(i&1&&(r(0,"mat-card")(1,"mat-card-header")(2,"mat-card-title"),o(3,"Ride Payment"),n()(),r(4,"mat-card-content")(5,"div",2)(6,"div",3)(7,"span",4),o(8,"Pickup:"),n(),r(9,"span",5),o(10),n()(),r(11,"div",3)(12,"span",4),o(13,"Destination:"),n(),r(14,"span",5),o(15),n()(),r(16,"div",3)(17,"span",4),o(18,"Date:"),n(),r(19,"span",5),o(20),U(21,"date"),n()(),r(22,"div",3)(23,"span",4),o(24,"Status:"),n(),r(25,"span",6),o(26),n()(),r(27,"div",3)(28,"span",4),o(29,"Payment Status:"),n(),r(30,"span",6),o(31),n()(),r(32,"div",7)(33,"span",4),o(34,"Amount:"),n(),r(35,"span",5),o(36),n()()(),p(37,Nt,1,0,"mat-divider",8)(38,Ut,4,0,"div",9)(39,zt,14,4,"div",10)(40,Ht,6,3,"div",11)(41,Gt,5,2,"div",12),n()()),i&2){let e=d();a(10),h(e.ride.pickup_location),a(5),h(e.ride.dropoff_location),a(5),h($(21,13,e.ride.pickup_time,"medium")),a(5),m("ngClass","status-"+e.ride.status),a(),h(e.ride.status),a(4),m("ngClass","payment-"+(e.ride.payment_status||"pending")),a(),_(" ",e.ride.payment_status||"pending"," "),a(5),h("$"+e.getDisplayAmount()),a(),m("ngIf",e.canPay()),a(),m("ngIf",e.canPay()&&!e.sdkLoaded),a(),m("ngIf",e.canPay()&&e.sdkLoaded),a(),m("ngIf",e.canRequestRefund()),a(),m("ngIf",e.paymentResult)}}var Z=class i{constructor(t,e,s,c){this.paymentService=t;this.rideService=e;this.snackBar=s;this.authService=c}ride;cardElement;paymentCompleted=new oe;processing=!1;stripe;card;sdkLoaded=!1;cardError="";cardComplete=!1;paymentResult=null;ngOnInit(){return y(this,null,function*(){this.ride&&!this.ride.amount&&!this.ride.fare&&this.estimateFare(),this.loadStripeScript();let t=yield this._loadStripe(ie.stripePublishableKey)})}ngAfterViewInit(){this.initializeCard()}_loadStripe(t){return y(this,null,function*(){let e=yield Le(t);this.sdkLoaded=!0})}loadStripeScript(){if(window.Stripe){this.initializeStripe();return}let t=document.createElement("script");t.src="https://js.stripe.com/v3/",t.async=!0,t.onload=()=>{this.initializeStripe()},document.body.appendChild(t)}initializeStripe(){if(!window.Stripe){this.snackBar.open("Stripe SDK not available","Close",{duration:3e3});return}try{this.stripe=window.Stripe(ie.stripePublishableKey),setTimeout(()=>this.initializeCard(),100)}catch(t){console.error("Error initializing Stripe:",t),this.snackBar.open("Error initializing Stripe payments. Check your credentials.","Close",{duration:5e3})}}initializeCard(){if(!this.cardElement||!this.cardElement.nativeElement||!this.stripe){setTimeout(()=>this.initializeCard(),100);return}try{let t=this.stripe.elements();this.card=t.create("card",{style:{base:{iconColor:"#666EE8",color:"#31325F",fontWeight:400,fontFamily:'"Helvetica Neue", Helvetica, sans-serif',fontSize:"16px","::placeholder":{color:"#CFD7E0"}}}}),this.card.mount(this.cardElement.nativeElement),this.card.on("change",e=>{this.cardError=e.error?e.error.message:"",this.cardComplete=e.complete}),this.sdkLoaded=!0}catch(t){console.error("Error initializing Stripe card:",t),this.snackBar.open("Error initializing Stripe card form","Close",{duration:5e3})}}calculateAmount(){return 15}estimateFare(){return y(this,null,function*(){if(this.ride)try{let{fare:t}=yield this.paymentService.estimateFare(this.ride.pickup_location,this.ride.dropoff_location);yield this.rideService.updateRide(this.ride.id,{fare:t})}catch(t){console.error("Error estimating fare:",t)}})}canPay(){return this.ride?this.ride.status==="completed"&&(!this.ride.payment_status||this.ride.payment_status==="pending"||this.ride.payment_status==="failed"):!1}canRequestRefund(){return this.ride?this.ride.payment_status==="paid"||this.ride.payment_status==="completed":!1}processPayment(){return y(this,null,function*(){if(!(!this.ride||!this.canPay()||!this.stripe||!this.card)){this.processing=!0,this.paymentResult=null;try{let t=this.ride.amount||this.ride.fare||this.calculateAmount(),{paymentMethod:e,error:s}=yield this.stripe.createPaymentMethod({type:"card",card:this.card});if(s||s)throw s;let c={amount:t*100,currency:"usd",description:"Customer pamyment for ride",payment_method:e.id};console.log(c);let{data:l,error:f}=yield this.authService.supabase.functions.invoke("stripe",{body:c});if(f)throw console.error("Error creating payment intent:",f),new Error(`Failed to create payment intent: ${f.message}`);if(console.log("Payment intent created:",l),!l||!l.client_secret)throw new Error("No client secret returned from payment intent creation");let R=l.client_secret,{error:M,paymentIntent:P}=yield this.stripe.confirmCardPayment(R,{payment_method:e.id});if(M)throw M;this.paymentResult={success:!0,paymentIntent:P},yield this.rideService.updateRide(this.ride.id,{payment_status:"paid",payment_id:P.id,amount:t}),this.snackBar.open("Payment processed successfully!","Close",{duration:3e3}),this.paymentCompleted.emit()}catch(t){console.error("Error processing payment:",t),this.paymentResult={success:!1,error:{message:t.message||"An unknown error occurred"}},this.snackBar.open(`Payment error: ${t.message}`,"Close",{duration:5e3})}finally{this.processing=!1}}})}getDisplayAmount(){return this.ride?(this.ride.amount||this.ride.fare||this.calculateAmount()).toString():"0"}requestRefund(){return y(this,null,function*(){if(!(!this.ride||!this.canRequestRefund())){this.processing=!0,this.paymentResult=null;try{(yield this.paymentService.processRefund(this.ride.id))?(this.paymentResult={success:!0,refund:!0},this.snackBar.open("Refund processed successfully!","Close",{duration:3e3}),this.paymentCompleted.emit()):(this.paymentResult={success:!1,refund:!0,error:{message:"Failed to process refund"}},this.snackBar.open("Refund request failed. Please try again.","Close",{duration:3e3}))}catch(t){console.error("Error processing refund:",t),this.paymentResult={success:!1,refund:!0,error:{message:t.message||"An unknown error occurred"}},this.snackBar.open(`Refund error: ${t.message}`,"Close",{duration:5e3})}finally{this.processing=!1}}})}static \u0275fac=function(e){return new(e||i)(u(A),u(B),u(O),u(q))};static \u0275cmp=w({type:i,selectors:[["app-ride-payment"]],viewQuery:function(e,s){if(e&1&&ae(At,5),e&2){let c;se(c=ce())&&(s.cardElement=c.first)}},inputs:{ride:"ride"},outputs:{paymentCompleted:"paymentCompleted"},decls:1,vars:1,consts:[["cardElement",""],[4,"ngIf"],[1,"payment-details"],[1,"detail-row"],[1,"label"],[1,"value"],[1,"value","status-badge",3,"ngClass"],[1,"detail-row","amount"],["class","section-divider",4,"ngIf"],["class","sdk-status",4,"ngIf"],["class","stripe-payment-form",4,"ngIf"],["class","payment-actions",4,"ngIf"],["class","payment-result",4,"ngIf"],[1,"section-divider"],[1,"sdk-status"],["diameter","30"],[1,"stripe-payment-form"],[1,"payment-instruction"],[1,"card-element"],["class","card-errors",4,"ngIf"],[1,"payment-actions"],["mat-raised-button","","color","primary",3,"click","disabled"],["class","processing-indicator",4,"ngIf"],[1,"card-errors"],[1,"processing-indicator"],["diameter","24"],["mat-raised-button","","color","warn",3,"click","disabled"],[1,"payment-result"],[3,"ngClass"]],template:function(e,s){e&1&&p(0,Qt,42,16,"mat-card",1),e&2&&m("ngIf",s.ride)},dependencies:[I,me,E,z,V,K,J,X,W,T,H,F,D,$e,Ue,Q,G,ze,je],styles:["[_nghost-%COMP%]{display:block;margin:20px}.payment-details[_ngcontent-%COMP%]{margin-bottom:20px}.detail-row[_ngcontent-%COMP%]{display:flex;margin-bottom:8px;align-items:center}.label[_ngcontent-%COMP%]{font-weight:500;width:120px;color:#0009}.value[_ngcontent-%COMP%]{flex:1}.amount[_ngcontent-%COMP%]{font-size:1.2em;font-weight:500;margin-top:16px}.amount[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{color:#3f51b5}.status-badge[_ngcontent-%COMP%]{display:inline-block;padding:4px 8px;border-radius:4px;text-transform:capitalize;font-size:.9em}.status-requested[_ngcontent-%COMP%]{background-color:#ffeb3b;color:#000}.status-assigned[_ngcontent-%COMP%]{background-color:#2196f3;color:#fff}.status-in-progress[_ngcontent-%COMP%]{background-color:#ff9800;color:#fff}.status-completed[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff}.status-canceled[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.payment-pending[_ngcontent-%COMP%]{background-color:#ffeb3b;color:#000}.payment-paid[_ngcontent-%COMP%], .payment-completed[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff}.payment-failed[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.payment-refunded[_ngcontent-%COMP%]{background-color:#9e9e9e;color:#fff}.payment-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;align-items:flex-start;margin-top:16px}.processing-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-top:8px}.section-divider[_ngcontent-%COMP%]{margin:24px 0}.stripe-payment-form[_ngcontent-%COMP%]{margin-top:24px}.payment-instruction[_ngcontent-%COMP%]{margin-bottom:16px;color:#0009}.card-element[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:4px;padding:12px;background-color:#fff;margin-bottom:16px}.card-errors[_ngcontent-%COMP%]{color:#f44336;font-size:.9em;margin-bottom:16px}.sdk-status[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:16px;margin:24px 0}.payment-result[_ngcontent-%COMP%]{margin-top:24px;padding:16px;border-radius:4px;background-color:#f5f5f5}.success-message[_ngcontent-%COMP%]{color:#4caf50;font-weight:500}.error-message[_ngcontent-%COMP%]{color:#f44336;font-weight:500}"]})};function Kt(i,t){i&1&&(r(0,"th",22),o(1,"Pickup"),n())}function Wt(i,t){if(i&1&&(r(0,"td",23),o(1),n()),i&2){let e=t.$implicit;a(),h(e.pickup_location)}}function Jt(i,t){i&1&&(r(0,"th",22),o(1,"Dropoff"),n())}function Xt(i,t){if(i&1&&(r(0,"td",23),o(1),n()),i&2){let e=t.$implicit;a(),h(e.dropoff_location)}}function Yt(i,t){i&1&&(r(0,"th",22),o(1,"Time"),n())}function Zt(i,t){if(i&1&&(r(0,"td",23),o(1),n()),i&2){let e=t.$implicit,s=d();a(),h(s.formatDate(e.pickup_time))}}function ei(i,t){i&1&&(r(0,"th",22),o(1,"Status"),n())}function ti(i,t){if(i&1&&(r(0,"td",23)(1,"mat-chip-listbox")(2,"mat-chip"),o(3),n()()()),i&2){let e=t.$implicit,s=d();a(2),ee(s.getStatusClass(e.status)),a(),_(" ",s.formatStatus(e.status)," ")}}function ii(i,t){i&1&&(r(0,"th",22),o(1,"Payment"),n())}function ni(i,t){if(i&1&&(r(0,"mat-chip-listbox")(1,"mat-chip"),o(2),n()()),i&2){let e=d().$implicit;a(),ee("payment-status-"+e.payment_status),a(),_(" ",e.payment_status," ")}}function ri(i,t){i&1&&(r(0,"span"),o(1,"-"),n())}function oi(i,t){if(i&1&&(r(0,"td",23),p(1,ni,3,3,"mat-chip-listbox",24)(2,ri,2,0,"span",24),n()),i&2){let e=t.$implicit;a(),m("ngIf",e.payment_status),a(),m("ngIf",!e.payment_status)}}function ai(i,t){i&1&&(r(0,"th",22),o(1,"Fare"),n())}function si(i,t){if(i&1&&(r(0,"td",23),o(1),n()),i&2){let e=t.$implicit;a(),_(" ",e.amount||e.fare?"$"+(e.amount||e.fare).toFixed(2):"-"," ")}}function ci(i,t){i&1&&(r(0,"th",22),o(1,"Actions"),n())}function mi(i,t){if(i&1){let e=S();r(0,"button",28),v("click",function(){g(e);let c=d().$implicit,l=d();return C(l.cancelRide(c.id))}),r(1,"mat-icon"),o(2,"cancel"),n()()}}function di(i,t){if(i&1){let e=S();r(0,"button",29),v("click",function(){g(e);let c=d().$implicit,l=d();return C(l.viewPayment(c))}),r(1,"mat-icon"),o(2,"payment"),n()()}}function li(i,t){if(i&1){let e=S();r(0,"td",23),p(1,mi,3,0,"button",25)(2,di,3,0,"button",26),r(3,"button",27),v("click",function(){let c=g(e).$implicit,l=d();return C(l.viewRideDetails(c.id))}),r(4,"mat-icon"),o(5,"visibility"),n()()()}if(i&2){let e=t.$implicit;a(),m("ngIf",e.status==="requested"),a(),m("ngIf",e.status==="completed")}}function pi(i,t){i&1&&x(0,"tr",30)}function ui(i,t){i&1&&x(0,"tr",31)}function fi(i,t){if(i&1){let e=S();r(0,"button",28),v("click",function(c){g(e);let l=d().$implicit;return d().cancelRide(l.id),C(c.stopPropagation())}),r(1,"mat-icon"),o(2,"cancel"),n()()}}function _i(i,t){if(i&1){let e=S();r(0,"button",29),v("click",function(c){g(e);let l=d().$implicit;return d().viewPayment(l),C(c.stopPropagation())}),r(1,"mat-icon"),o(2,"payment"),n()()}}function gi(i,t){if(i&1&&(r(0,"span"),o(1),n()),i&2){let e=d().$implicit;a(),h(e.payment_status)}}function Ci(i,t){i&1&&(r(0,"span"),o(1,"-"),n())}function xi(i,t){if(i&1){let e=S();r(0,"mat-expansion-panel")(1,"mat-expansion-panel-header")(2,"mat-panel-title"),o(3),n(),r(4,"mat-panel-description")(5,"div",32)(6,"span",33),o(7),U(8,"date"),n(),p(9,fi,3,0,"button",25)(10,_i,3,0,"button",26),n()()(),r(11,"div",34)(12,"p")(13,"strong"),o(14,"To:"),n(),o(15),n(),r(16,"p")(17,"strong"),o(18,"Time:"),n(),o(19),U(20,"date"),n(),r(21,"p")(22,"strong"),o(23,"Status:"),n(),o(24),n(),r(25,"p")(26,"strong"),o(27,"Payment:"),n(),p(28,gi,2,1,"span",24)(29,Ci,2,0,"span",24),n(),r(30,"p")(31,"strong"),o(32,"Fare:"),n(),o(33),n()(),r(34,"mat-action-row")(35,"button",27),v("click",function(){let c=g(e).$implicit,l=d();return C(l.viewRideDetails(c.id))}),r(36,"mat-icon"),o(37,"visibility"),n()()()()}if(i&2){let e=t.$implicit,s=d();a(3),_(" ",e.pickup_location," "),a(4),h($(8,10,e.pickup_time,"shortTime")),a(2),m("ngIf",e.status==="requested"),a(),m("ngIf",e.status==="completed"),a(5),_(" ",e.dropoff_location,""),a(4),_(" ",$(20,13,e.pickup_time,"short"),""),a(5),_(" ",s.formatStatus(e.status),""),a(4),m("ngIf",e.payment_status),a(),m("ngIf",!e.payment_status),a(4),_(" ",e.amount||e.fare?"$"+(e.amount||e.fare).toFixed(2):"-","")}}function vi(i,t){if(i&1){let e=S();r(0,"div",35)(1,"app-ride-payment",36),v("paymentCompleted",function(){g(e);let c=d();return C(c.closePayment())}),n(),r(2,"button",37),v("click",function(){g(e);let c=d();return C(c.closePayment())}),r(3,"mat-icon"),o(4,"close"),n()()()}if(i&2){let e=d();a(),m("ride",e.selectedRide)}}function yi(i,t){if(i&1){let e=S();r(0,"div",38)(1,"app-ride-detail",39),v("paymentRequested",function(c){g(e);let l=d();return C(l.viewPayment(c))})("rideUpdated",function(c){g(e);let l=d();return C(l.onRideUpdated(c))}),n()()}if(i&2){let e=d();a(),m("rideId",e.selectedRideId)("onClose",e.closeRideDetails.bind(e))}}var wt=class i{constructor(t,e,s,c,l,f,R){this.authService=t;this.rideService=e;this.messageService=s;this.paymentService=c;this.router=l;this.dialog=f;this.snackBar=R}rides=[];displayedColumns=["pickup_location","dropoff_location","pickup_time","status","payment_status","fare","actions"];currentUser=null;selectedRide=null;selectedRideId=null;ridesSubscription=null;ngOnInit(){return y(this,null,function*(){this.currentUser=yield this.authService.getCurrentUser(),this.currentUser&&(yield this.loadUserRides(),this.ridesSubscription=this.rideService.rides$.subscribe(t=>{this.currentUser&&(this.rides=t.filter(e=>e.rider_id===this.currentUser.id))}))})}ngOnDestroy(){this.ridesSubscription&&this.ridesSubscription.unsubscribe()}loadUserRides(){return y(this,null,function*(){if(this.currentUser)try{this.rides=yield this.rideService.getUserRides(this.currentUser.id)}catch(t){console.error("Error loading user rides:",t),this.snackBar.open("Failed to load rides","Close",{duration:3e3})}})}cancelRide(t){return y(this,null,function*(){try{if(yield this.rideService.cancelRide(t))this.snackBar.open("Ride canceled successfully","Close",{duration:3e3}),yield this.loadUserRides();else throw new Error("Failed to cancel ride")}catch(e){console.error("Error canceling ride:",e),this.snackBar.open("Failed to cancel ride","Close",{duration:3e3})}})}formatDate(t){return new Date(t).toLocaleString()}formatStatus(t){return t.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}getStatusClass(t){return`status-chip status-${t}`}openChat(t){return y(this,null,function*(){try{let e=yield this.messageService.getOrCreateThreadForRide(t);this.router.navigate(["/dashboard","rider","messages",e.id])}catch(e){console.error("Error opening chat:",e),this.snackBar.open("Failed to open chat","Close",{duration:3e3})}})}viewPayment(t){this.selectedRide=t}closePayment(){this.selectedRide=null,this.loadUserRides()}viewRideDetails(t){this.selectedRideId=t}closeRideDetails(){this.selectedRideId=null,this.loadUserRides()}onRideUpdated(t){this.loadUserRides()}trackByRideId(t,e){return e.id}static \u0275fac=function(e){return new(e||i)(u(q),u(B),u(Ke),u(A),u(le),u(pe),u(O))};static \u0275cmp=w({type:i,selectors:[["app-rider"]],decls:36,vars:7,consts:[[1,"dashboard-container"],["label","Request Ride"],["label","Ride History"],[1,"table-container"],[1,"desktop-view"],["mat-table","",1,"ride-table",3,"dataSource"],["matColumnDef","pickup_location"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","dropoff_location"],["matColumnDef","pickup_time"],["matColumnDef","status"],["matColumnDef","payment_status"],["matColumnDef","fare"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],[1,"mobile-view"],["multi",""],[4,"ngFor","ngForOf","ngForTrackBy"],["class","payment-overlay",4,"ngIf"],["class","ride-detail-overlay",4,"ngIf"],["mat-header-cell",""],["mat-cell",""],[4,"ngIf"],["mat-icon-button","","color","warn","matTooltip","Cancel Ride",3,"click",4,"ngIf"],["mat-icon-button","","color","accent","matTooltip","View Payment",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","View Details",3,"click"],["mat-icon-button","","color","warn","matTooltip","Cancel Ride",3,"click"],["mat-icon-button","","color","accent","matTooltip","View Payment",3,"click"],["mat-header-row",""],["mat-row",""],[1,"ride-actions-header"],[1,"ride-time"],[1,"ride-details"],[1,"payment-overlay"],[3,"paymentCompleted","ride"],["mat-icon-button","",1,"close-payment-button",3,"click"],[1,"ride-detail-overlay"],[3,"paymentRequested","rideUpdated","rideId","onClose"]],template:function(e,s){e&1&&(r(0,"div",0)(1,"mat-tab-group")(2,"mat-tab",1),x(3,"app-ride-request"),n(),r(4,"mat-tab",2)(5,"div",3)(6,"div",4)(7,"table",5),b(8,6),p(9,Kt,2,0,"th",7)(10,Wt,2,1,"td",8),k(),b(11,9),p(12,Jt,2,0,"th",7)(13,Xt,2,1,"td",8),k(),b(14,10),p(15,Yt,2,0,"th",7)(16,Zt,2,1,"td",8),k(),b(17,11),p(18,ei,2,0,"th",7)(19,ti,4,3,"td",8),k(),b(20,12),p(21,ii,2,0,"th",7)(22,oi,3,2,"td",8),k(),b(23,13),p(24,ai,2,0,"th",7)(25,si,2,1,"td",8),k(),b(26,14),p(27,ci,2,0,"th",7)(28,li,6,2,"td",8),k(),p(29,pi,1,0,"tr",15)(30,ui,1,0,"tr",16),n()(),r(31,"div",17)(32,"mat-accordion",18),p(33,xi,38,16,"mat-expansion-panel",19),n()()()()()(),p(34,vi,5,1,"div",20)(35,yi,2,2,"div",21)),e&2&&(a(7),m("dataSource",s.rides),a(22),m("matHeaderRowDef",s.displayedColumns),a(),m("matRowDefColumns",s.displayedColumns),a(3),m("ngForOf",s.rides)("ngForTrackBy",s.trackByRideId),a(),m("ngIf",s.selectedRide),a(),m("ngIf",s.selectedRideId))},dependencies:[I,de,E,z,V,Qe,He,Ge,mt,Ze,tt,ot,it,et,at,nt,rt,st,ct,Ye,Je,Xe,T,L,F,D,G,lt,dt,Q,xt,Ct,pt,ut,ft,gt,_t,Y,Z,We],styles:[".dashboard-container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto;background-color:#f5f5f5}.table-container[_ngcontent-%COMP%]{margin:20px}.ride-table[_ngcontent-%COMP%]{width:100%}.status-chip[_ngcontent-%COMP%]{border-radius:16px;padding:4px 12px;color:#fff;font-weight:500}.status-requested[_ngcontent-%COMP%]{background-color:#ff9800}.status-assigned[_ngcontent-%COMP%]{background-color:#2196f3}.status-in-progress[_ngcontent-%COMP%]{background-color:#673ab7}.status-completed[_ngcontent-%COMP%]{background-color:#4caf50}.status-canceled[_ngcontent-%COMP%]{background-color:#f44336}.payment-status-pending[_ngcontent-%COMP%]{background-color:#ffeb3b;color:#000}.payment-status-paid[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff}.payment-status-failed[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.payment-status-refunded[_ngcontent-%COMP%]{background-color:#9e9e9e;color:#fff}.payment-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.close-payment-button[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;background-color:#fff}.ride-detail-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.mobile-view[_ngcontent-%COMP%]{display:none}.desktop-view[_ngcontent-%COMP%]{display:block}.ride-actions-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.ride-time[_ngcontent-%COMP%]{font-size:12px;color:#666}@media (max-width: 600px){.desktop-view[_ngcontent-%COMP%]{display:none}.mobile-view[_ngcontent-%COMP%]{display:block}.dashboard-container[_ngcontent-%COMP%]{padding:0}.table-container[_ngcontent-%COMP%]{margin:0}.mat-tab-body-content[_ngcontent-%COMP%]{overflow:hidden}}.mobile-view[_ngcontent-%COMP%]   .mat-expansion-panel[_ngcontent-%COMP%]{margin:8px 0}.mobile-view[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{font-size:14px}.mobile-view[_ngcontent-%COMP%]   .mat-panel-title[_ngcontent-%COMP%]{font-weight:500}.mobile-view[_ngcontent-%COMP%]   .mat-panel-description[_ngcontent-%COMP%]{justify-content:flex-end;align-items:center}.mobile-view[_ngcontent-%COMP%]   .ride-details[_ngcontent-%COMP%]{padding:0 24px 16px;font-size:14px}.mobile-view[_ngcontent-%COMP%]   .ride-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0}.mobile-view[_ngcontent-%COMP%]   .mat-action-row[_ngcontent-%COMP%]{justify-content:flex-end;padding:8px 12px 8px 24px}"]})};export{wt as RiderComponent};

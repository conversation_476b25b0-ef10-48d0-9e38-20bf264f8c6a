import{a as Ee}from"./chunk-EACHO2FA.js";import{a as he,b as fe}from"./chunk-WSXVBUWR.js";import{b as Ce}from"./chunk-IT7B5FSQ.js";import{C as ge,F as ue,H as _e,I as ve,J as I,b as ne,f as oe,g as ce,h as de,j as me,k as le,t as pe}from"./chunk-AG3SD6JT.js";import{$b as J,Ea as z,Eb as $,Ed as v,Fb as L,Fc as ie,Gb as H,Gd as se,Jb as G,Jd as ae,Kb as p,La as c,Lb as D,Ld as be,Md as Se,Nd as ye,Ob as Q,Pa as y,Pb as Y,Pd as we,Qb as K,Qd as Re,Rd as Me,Vb as Z,Wa as u,Xa as w,Z as F,_ as b,aa as W,ab as h,ca as T,da as S,g as j,gb as l,gc as E,hb as m,ib as R,jb as M,kb as U,na as O,oa as B,qb as o,rb as d,sb as f,tc as X,td as N,uc as ee,vb as C,vc as re,yb as V,za as q,zb as _,zc as te}from"./chunk-ST4QC4E3.js";import{a as P,b as k,i as n}from"./chunk-ODN5LVDJ.js";var x=class s{constructor(t,e){this.smsService=t;this.authService=e;this._supabase=e.supabase,this.initializeRealTimeSubscription()}_supabase;ridesSubject=new j([]);rides$=this.ridesSubject.asObservable();rideSubscription=null;get supabase(){return this._supabase}ngOnDestroy(){this.rideSubscription&&(console.log("\u{1F9F9} Cleaning up rides realtime subscription"),this.rideSubscription.unsubscribe(),this.rideSubscription=null)}initializeRealTimeSubscription(){this.rideSubscription&&this.rideSubscription.unsubscribe(),this.rideSubscription=this._supabase.channel("rides-channel").on("postgres_changes",{event:"*",schema:"public",table:"rides"},t=>n(this,null,function*(){console.log("\u{1F504} Realtime event received:",t.eventType,t.new||t.old);try{yield this.refreshRides(),console.log("\u2705 Rides refreshed successfully after realtime event")}catch(e){console.error("\u274C Error refreshing rides after realtime event:",e)}})).subscribe(t=>{console.log("\u{1F4E1} Realtime subscription status:",t),t==="SUBSCRIBED"?console.log("\u2705 Successfully subscribed to rides realtime updates"):t==="CHANNEL_ERROR"&&(console.error("\u274C Realtime subscription error, attempting to reconnect..."),setTimeout(()=>{this.initializeRealTimeSubscription()},5e3))})}refreshRides(){return n(this,null,function*(){try{let{data:t,error:e}=yield this._supabase.from("rides").select("*").order("created_at",{ascending:!1});if(e)throw e;this.ridesSubject.next(t||[])}catch(t){console.error("Error refreshing rides:",t)}})}getAllRides(){return n(this,null,function*(){try{let{data:t,error:e}=yield this._supabase.from("rides").select("*").order("created_at",{ascending:!1});if(e)throw e;return this.ridesSubject.next(t||[]),t||[]}catch(t){return console.error("Error fetching all rides:",t),this.ridesSubject.value}})}getRidesByStatus(t){return n(this,null,function*(){try{let{data:e,error:r}=yield this._supabase.from("rides").select("*").eq("status",t).order("created_at",{ascending:!1});if(r)throw r;return e}catch(e){return console.error(`Error fetching rides with status ${t}:`,e),[]}})}assignRideToDriver(t,e){return n(this,null,function*(){try{let r=yield this.getRide(t);if(!r||r.status!=="requested"&&r.status!=="assigned")throw new Error("Ride is no longer available for assignment");let{error:i}=yield this._supabase.from("rides").update({driver_id:e,status:"assigned",updated_at:new Date().toISOString()}).eq("id",t).in("status",["requested","assigned"]);if(i)throw i;let a=yield this.getRide(t);return a&&(setTimeout(()=>n(this,null,function*(){try{yield this.smsService.sendRideAssignmentNotifications(a,e)}catch(g){console.error("Error sending SMS notifications:",g)}}),0),console.log(`Sending ride assignment notifications for ride ${t} to rider and driver ${e}`)),!0}catch(r){throw console.error("Error assigning ride to driver:",r),r}})}updateRideStatus(t,e){return n(this,null,function*(){try{let r=yield this.getRide(t);if(!r)throw new Error("Ride not found");if(!this.isValidStatusTransition(r.status,e))throw new Error(`Invalid status transition from ${r.status} to ${e}`);let{error:i}=yield this._supabase.from("rides").update({status:e,updated_at:new Date().toISOString()}).eq("id",t);if(i)throw i;yield this.refreshRides();let a=yield this.getRide(t);return a&&(setTimeout(()=>n(this,null,function*(){try{e==="canceled"?yield this.smsService.sendRideCancellationNotifications(a):e==="completed"?(yield this.smsService.sendRideCompletionNotification(a),yield this.smsService.sendAdminRideStatusNotification(a,e)):e==="in-progress"&&(yield this.smsService.sendRideStatusUpdateNotifications(a,e),yield this.smsService.sendAdminRideStatusNotification(a,e))}catch(g){console.error("Error sending status update notifications:",g)}}),0),console.log(`Sending ride status update (${e}) notifications for ride ${t}`)),!0}catch(r){throw console.error("Error updating ride status:",r),r}})}isValidStatusTransition(t,e){return{requested:["assigned","canceled"],assigned:["in-progress","canceled","requested"],"in-progress":["completed","canceled"],completed:[],canceled:[]}[t]?.includes(e)||!1}createRide(t){return n(this,null,function*(){try{let e=k(P({},t),{payment_status:t.payment_status||"pending"}),{data:r,error:i}=yield this._supabase.from("rides").insert([e]).select().single();if(i)throw i;let a=this.ridesSubject.value;return this.ridesSubject.next([...a,r]),setTimeout(()=>n(this,null,function*(){}),0),console.log(`Sending ride booking notifications for ride ${r.id}`),r}catch(e){throw console.error("Error creating ride:",e),e}})}getUserRides(t){return n(this,null,function*(){try{let{data:e,error:r}=yield this._supabase.from("rides").select("*").eq("rider_id",t).order("created_at",{ascending:!1});if(r)throw r;return this.ridesSubject.next(e),e}catch(e){return console.error("Error fetching user rides:",e),[]}})}getDriverRides(t){return n(this,null,function*(){try{let{data:e,error:r}=yield this._supabase.from("rides").select("*").eq("driver_id",t).order("created_at",{ascending:!1});if(r)throw r;return e}catch(e){return console.error("Error fetching driver rides:",e),[]}})}getAvailableRides(){return n(this,null,function*(){try{let{data:t,error:e}=yield this._supabase.from("rides").select("*").eq("status","requested").order("created_at",{ascending:!1});if(e)throw e;return t}catch(t){return console.error("Error fetching available rides:",t),[]}})}acceptRide(t,e){return n(this,null,function*(){return this.assignRideToDriver(t,e)})}startRide(t){return n(this,null,function*(){return this.updateRideStatus(t,"in-progress")})}completeRide(t){return n(this,null,function*(){return this.updateRideStatus(t,"completed")})}getRide(t){return n(this,null,function*(){try{let{data:e,error:r}=yield this._supabase.from("rides").select("*").eq("id",t).single();if(r)throw r;return e}catch(e){return console.error("Error fetching ride:",e),null}})}updateRide(t,e){return n(this,null,function*(){try{let{error:r}=yield this._supabase.from("rides").update(k(P({},e),{updated_at:new Date().toISOString()})).eq("id",t);if(r)throw r;return e.payment_status&&(e.payment_status==="paid"||e.payment_status==="completed")&&setTimeout(()=>n(this,null,function*(){try{let i=yield this.getRide(t);i&&e.amount&&(yield this.smsService.sendPaymentConfirmationNotification(i,e.amount))}catch(i){console.error("Error sending payment confirmation notification:",i)}}),0),yield this.getAllRides(),!0}catch(r){return console.error("Error updating ride:",r),!1}})}cancelRide(t){return n(this,null,function*(){return this.updateRideStatus(t,"canceled")})}cancelDriverAssignment(t){return n(this,null,function*(){try{let e=yield this.getRide(t);if(!e)throw new Error("Ride not found");if(e.status!=="assigned")throw new Error("Ride is not in assigned status");let{error:r}=yield this._supabase.from("rides").update({driver_id:null,status:"requested",updated_at:new Date().toISOString()}).eq("id",t);if(r)throw r;let i=yield this.getRide(t);return i&&(setTimeout(()=>n(this,null,function*(){try{yield this.smsService.sendDriverCancellationNotification(i)}catch(a){console.error("Error sending driver cancellation notifications:",a)}}),0),console.log(`Driver assignment cancelled for ride ${t}, ride returned to requested status`)),!0}catch(e){throw console.error("Error cancelling driver assignment:",e),e}})}filterRides(t,e){return t.filter(r=>{if(e.status&&r.status!==e.status||e.riderId&&r.rider_id!==e.riderId||e.driverId&&r.driver_id!==e.driverId)return!1;if(e.dateRange){let i=new Date(r.created_at),a=e.dateRange.start,g=e.dateRange.end;if(i<a||i>g)return!1}return!0})}static \u0275fac=function(e){return new(e||s)(T(Ce),T(I))};static \u0275prov=F({token:s,factory:s.\u0275fac,providedIn:"root"})};var De=["determinateSpinner"];function Ne(s,t){if(s&1&&(O(),o(0,"svg",11),f(1,"circle",12),d()),s&2){let e=_();l("viewBox",e._viewBox()),c(),R("stroke-dasharray",e._strokeCircumference(),"px")("stroke-dashoffset",e._strokeCircumference()/2,"px")("stroke-width",e._circleStrokeWidth(),"%"),l("r",e._circleRadius())}}var Ae=new W("mat-progress-spinner-default-options",{providedIn:"root",factory:je});function je(){return{diameter:Ie}}var Ie=100,Fe=10,xe=(()=>{class s{_elementRef=S(q);_noopAnimations;get color(){return this._color||this._defaultColor}set color(e){this._color=e}_color;_defaultColor="primary";_determinateCircle;constructor(){let e=S(z,{optional:!0}),r=S(Ae);this._noopAnimations=e==="NoopAnimations"&&!!r&&!r._forceAnimations,this.mode=this._elementRef.nativeElement.nodeName.toLowerCase()==="mat-spinner"?"indeterminate":"determinate",r&&(r.color&&(this.color=this._defaultColor=r.color),r.diameter&&(this.diameter=r.diameter),r.strokeWidth&&(this.strokeWidth=r.strokeWidth))}mode;get value(){return this.mode==="determinate"?this._value:0}set value(e){this._value=Math.max(0,Math.min(100,e||0))}_value=0;get diameter(){return this._diameter}set diameter(e){this._diameter=e||0}_diameter=Ie;get strokeWidth(){return this._strokeWidth??this.diameter/10}set strokeWidth(e){this._strokeWidth=e||0}_strokeWidth;_circleRadius(){return(this.diameter-Fe)/2}_viewBox(){let e=this._circleRadius()*2+this.strokeWidth;return`0 0 ${e} ${e}`}_strokeCircumference(){return 2*Math.PI*this._circleRadius()}_strokeDashOffset(){return this.mode==="determinate"?this._strokeCircumference()*(100-this._value)/100:null}_circleStrokeWidth(){return this.strokeWidth/this.diameter*100}static \u0275fac=function(r){return new(r||s)};static \u0275cmp=u({type:s,selectors:[["mat-progress-spinner"],["mat-spinner"]],viewQuery:function(r,i){if(r&1&&$(De,5),r&2){let a;L(a=H())&&(i._determinateCircle=a.first)}},hostAttrs:["role","progressbar","tabindex","-1",1,"mat-mdc-progress-spinner","mdc-circular-progress"],hostVars:18,hostBindings:function(r,i){r&2&&(l("aria-valuemin",0)("aria-valuemax",100)("aria-valuenow",i.mode==="determinate"?i.value:null)("mode",i.mode),U("mat-"+i.color),R("width",i.diameter,"px")("height",i.diameter,"px")("--mdc-circular-progress-size",i.diameter+"px")("--mdc-circular-progress-active-indicator-width",i.diameter+"px"),M("_mat-animation-noopable",i._noopAnimations)("mdc-circular-progress--indeterminate",i.mode==="indeterminate"))},inputs:{color:"color",mode:"mode",value:[2,"value","value",E],diameter:[2,"diameter","diameter",E],strokeWidth:[2,"strokeWidth","strokeWidth",E]},exportAs:["matProgressSpinner"],decls:14,vars:11,consts:[["circle",""],["determinateSpinner",""],["aria-hidden","true",1,"mdc-circular-progress__determinate-container"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__determinate-circle-graphic"],["cx","50%","cy","50%",1,"mdc-circular-progress__determinate-circle"],["aria-hidden","true",1,"mdc-circular-progress__indeterminate-container"],[1,"mdc-circular-progress__spinner-layer"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-left"],[3,"ngTemplateOutlet"],[1,"mdc-circular-progress__gap-patch"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-right"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__indeterminate-circle-graphic"],["cx","50%","cy","50%"]],template:function(r,i){if(r&1&&(h(0,Ne,2,8,"ng-template",null,0,J),o(2,"div",2,1),O(),o(4,"svg",3),f(5,"circle",4),d()(),B(),o(6,"div",5)(7,"div",6)(8,"div",7),C(9,8),d(),o(10,"div",9),C(11,8),d(),o(12,"div",10),C(13,8),d()()()),r&2){let a=G(1);c(4),l("viewBox",i._viewBox()),c(),R("stroke-dasharray",i._strokeCircumference(),"px")("stroke-dashoffset",i._strokeDashOffset(),"px")("stroke-width",i._circleStrokeWidth(),"%"),l("r",i._circleRadius()),c(4),m("ngTemplateOutlet",a),c(2),m("ngTemplateOutlet",a),c(2),m("ngTemplateOutlet",a)}},dependencies:[te],styles:[`.mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}
`],encapsulation:2,changeDetection:0})}return s})();var Pe=(()=>{class s{static \u0275fac=function(r){return new(r||s)};static \u0275mod=w({type:s});static \u0275inj=b({imports:[v]})}return s})();var ar=(()=>{class s{get vertical(){return this._vertical}set vertical(e){this._vertical=N(e)}_vertical=!1;get inset(){return this._inset}set inset(e){this._inset=N(e)}_inset=!1;static \u0275fac=function(r){return new(r||s)};static \u0275cmp=u({type:s,selectors:[["mat-divider"]],hostAttrs:["role","separator",1,"mat-divider"],hostVars:7,hostBindings:function(r,i){r&2&&(l("aria-orientation",i.vertical?"vertical":"horizontal"),M("mat-divider-vertical",i.vertical)("mat-divider-horizontal",!i.vertical)("mat-divider-inset",i.inset))},inputs:{vertical:"vertical",inset:"inset"},decls:0,vars:0,template:function(r,i){},styles:[`.mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}
`],encapsulation:2,changeDetection:0})}return s})(),nr=(()=>{class s{static \u0275fac=function(r){return new(r||s)};static \u0275mod=w({type:s});static \u0275inj=b({imports:[v,v]})}return s})();var Be=(s,t)=>({sent:s,received:t});function qe(s,t){s&1&&(o(0,"div",9),f(1,"mat-spinner",10),o(2,"p"),p(3,"Loading messages..."),d()())}function ze(s,t){s&1&&(o(0,"div",11)(1,"p"),p(2,"No messages yet. Start the conversation!"),d()())}function Ue(s,t){if(s&1&&(o(0,"div",14)(1,"div",15),p(2),d(),o(3,"div",16),p(4),d()()),s&2){let e=t.$implicit,r=_(2);m("ngClass",Z(3,Be,e.sender_id===r.currentUserId,e.sender_id!==r.currentUserId)),c(2),D(e.content),c(2),D(r.formatTime(e.created_at))}}function Ve(s,t){if(s&1&&(o(0,"div",12),h(1,Ue,5,6,"div",13),d()),s&2){let e=_();c(),m("ngForOf",e.messages)}}var ke=class s{constructor(t,e,r){this.messageService=t;this.authService=e;this.rideService=r}threadId;rideId;messages=[];newMessage="";loading=!1;sending=!1;currentUserId="";receiverId="";ngOnInit(){return n(this,null,function*(){this.loading=!0;try{let t=yield this.authService.getCurrentUser();if(t&&(this.currentUserId=t.id),this.rideId&&!this.threadId){let e=yield this.messageService.getOrCreateThreadForRide(this.rideId);this.threadId=e.id}if(this.rideId){let e=yield this.rideService.getRide(this.rideId);e&&(this.receiverId=e.rider_id===this.currentUserId?e.driver_id:e.rider_id)}this.threadId&&(this.messages=yield this.messageService.getThreadMessages(this.threadId),yield this.messageService.markMessagesAsRead(this.threadId))}catch(t){console.error("Error initializing chat:",t)}finally{this.loading=!1}})}sendMessage(){return n(this,null,function*(){if(!(!this.newMessage||!this.threadId||!this.receiverId)){this.sending=!0;try{yield this.messageService.sendMessage(this.threadId,this.receiverId,this.newMessage),this.newMessage="",this.messages=yield this.messageService.getThreadMessages(this.threadId)}catch(t){console.error("Error sending message:",t)}finally{this.sending=!1}}})}formatTime(t){return new Date(t).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}static \u0275fac=function(e){return new(e||s)(y(Ee),y(I),y(x))};static \u0275cmp=u({type:s,selectors:[["app-ride-chat"]],inputs:{threadId:"threadId",rideId:"rideId"},decls:16,vars:5,consts:[[1,"chat-card"],[1,"messages-container"],["class","loading-container",4,"ngIf"],["class","no-messages",4,"ngIf"],["class","messages-list",4,"ngIf"],[1,"message-form",3,"ngSubmit"],["appearance","outline",1,"message-input"],["matInput","","name","newMessage","placeholder","Type a message...","autocomplete","off",3,"ngModelChange","ngModel"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[1,"loading-container"],["diameter","40"],[1,"no-messages"],[1,"messages-list"],["class","message-bubble",3,"ngClass",4,"ngFor","ngForOf"],[1,"message-bubble",3,"ngClass"],[1,"message-content"],[1,"message-time"]],template:function(e,r){e&1&&(o(0,"mat-card",0)(1,"mat-card-header")(2,"mat-card-title"),p(3,"Chat"),d()(),o(4,"mat-card-content")(5,"div",1),h(6,qe,4,0,"div",2)(7,ze,3,0,"div",3)(8,Ve,2,1,"div",4),d()(),o(9,"mat-card-actions")(10,"form",5),V("ngSubmit",function(){return r.sendMessage()}),o(11,"mat-form-field",6)(12,"input",7),K("ngModelChange",function(a){return Y(r.newMessage,a)||(r.newMessage=a),a}),d()(),o(13,"button",8)(14,"mat-icon"),p(15,"send"),d()()()()()),e&2&&(c(6),m("ngIf",r.loading),c(),m("ngIf",!r.loading&&r.messages.length===0),c(),m("ngIf",!r.loading&&r.messages.length>0),c(4),Q("ngModel",r.newMessage),c(),m("disabled",!r.newMessage||r.sending))},dependencies:[ie,X,ee,re,pe,le,ne,oe,ce,me,de,Me,be,we,ye,Re,Se,ue,ge,ve,_e,ae,se,fe,he,Pe,xe],styles:[".chat-card[_ngcontent-%COMP%]{margin:16px;max-width:800px}.messages-container[_ngcontent-%COMP%]{height:300px;overflow-y:auto;padding:16px;background-color:#f5f5f5;border-radius:4px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.no-messages[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:100%;color:#00000080}.messages-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.message-bubble[_ngcontent-%COMP%]{max-width:80%;padding:8px 12px;border-radius:16px;position:relative}.sent[_ngcontent-%COMP%]{align-self:flex-end;background-color:#2196f3;color:#fff;border-bottom-right-radius:4px}.received[_ngcontent-%COMP%]{align-self:flex-start;background-color:#fff;border-bottom-left-radius:4px}.message-content[_ngcontent-%COMP%]{word-break:break-word}.message-time[_ngcontent-%COMP%]{font-size:.7em;opacity:.7;text-align:right;margin-top:4px}.message-form[_ngcontent-%COMP%]{display:flex;gap:8px;width:100%;padding:0 16px 16px}.message-input[_ngcontent-%COMP%]{flex:1}"]})};export{x as a,xe as b,Pe as c,ar as d,nr as e,ke as f};

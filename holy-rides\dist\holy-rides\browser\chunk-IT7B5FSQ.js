import{J as f}from"./chunk-AG3SD6JT.js";import{Z as p,ca as u,g as v}from"./chunk-ST4QC4E3.js";import{a as m,b as g,i as c}from"./chunk-ODN5LVDJ.js";var h=class d{constructor(e){this.authService=e;this.supabase=e.supabase}supabase;usersSubject=new v([]);users$=this.usersSubject.asObservable();getAllUsers(){return c(this,null,function*(){try{let{data:e,error:r}=yield this.supabase.from("profiles").select("*").order("created_at",{ascending:!1});if(r)throw r;return this.usersSubject.next(e),e}catch(e){return console.error("Error fetching users:",e),[]}})}getUsersByRole(e){return c(this,null,function*(){try{let{data:r,error:o}=yield this.supabase.from("profiles").select("*").eq("role",e).order("created_at",{ascending:!1});if(o)throw o;return r}catch(r){return console.error(`Error fetching ${e}s:`,r),[]}})}approveDriver(e){return c(this,null,function*(){try{let{error:r}=yield this.supabase.from("profiles").update({is_approved:"TRUE"}).eq("id",e);if(r)throw r;let t=this.usersSubject.value.map(i=>i.id===e?g(m({},i),{is_approved:!0,is_active:!0}):i);return this.usersSubject.next(t),!0}catch(r){return console.error("Error approving driver:",r),!1}})}updateUserStatus(e,r){return c(this,null,function*(){try{let{data:o,error:t}=yield this.supabase.from("profiles").select("role, is_approved").eq("id",e).single();if(t)throw t;if(r&&o.role!=="admin"&&!o.is_approved)throw new Error("Cannot activate unapproved user");let{error:i}=yield this.supabase.from("profiles").update({is_approved:r,updated_at:new Date().toISOString()}).eq("id",e);if(i)throw i;let n=this.usersSubject.value.map(a=>a.id===e?g(m({},a),{is_approved:r}):a);return this.usersSubject.next(n),!0}catch(o){return console.error("Error updating user status:",o),!1}})}getUserById(e){return c(this,null,function*(){try{let{data:r,error:o}=yield this.supabase.from("profiles").select("*").eq("id",e).single();if(o)throw o;return r}catch(r){return console.error("Error fetching user by ID:",r),null}})}filterUsers(e,r){return e.filter(o=>{if(r.role&&o.role!==r.role||r.isApproved!==void 0&&o.role==="driver"&&o.is_approved!==r.isApproved)return!1;if(r.searchTerm){let t=r.searchTerm.toLowerCase(),i=o.full_name?.toLowerCase()||"",s=o.email.toLowerCase(),n=o.phone?.toLowerCase()||"";if(!i.includes(t)&&!s.includes(t)&&!n.includes(t))return!1}return!0})}static \u0275fac=function(r){return new(r||d)(u(f))};static \u0275prov=p({token:d,factory:d.\u0275fac,providedIn:"root"})};var y=class d{constructor(e,r){this.userService=e;this.authService=r;this.supabase=r.supabase}supabase;sendSms(e,r){return c(this,null,function*(){try{let o=this.formatPhoneNumber(e),{data:t,error:i}=yield this.supabase.functions.invoke("twilio",{body:{to:o,message:r,from:"+17272025413"}});if(i)throw console.error("Error calling Twilio lambda function:",i),i;if(!t||!t.sid)throw new Error("No message SID returned from Twilio lambda function");return console.log(`SMS sent successfully to ${e}, SID: ${t.sid}`),t.sid}catch(o){throw console.error("Error sending SMS:",o),o}})}sendRideAssignmentNotifications(e,r){return c(this,null,function*(){try{let[o,t]=yield Promise.all([this.userService.getUserById(e.rider_id),this.userService.getUserById(r)]);if(!o||!t)throw new Error("Could not find rider or driver information");if(!o.phone||!t.phone){console.warn("Phone number missing for rider or driver. SMS notification skipped.");return}let i=new Date(e.pickup_time).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});yield this.sendEnhancedDriverAssignedNotification(e,t);let s=`You have been assigned a new ride. Pick up ${o.full_name||"your rider"} at ${e.pickup_location} at ${i} and drop off at ${e.dropoff_location}. Rider phone: ${o.phone}`,n=yield Promise.allSettled([this.sendSmsWithRetry(t.phone,s)]),[a]=n;a.status==="rejected"?console.error("Failed to send SMS to driver:",a.reason):console.log("Ride assignment notification sent successfully to driver")}catch(o){console.error("Error sending ride assignment notifications:",o)}})}sendSmsWithRetry(e,r,o=2){return c(this,null,function*(){let t;for(let i=0;i<=o;i++)try{return i>0&&(yield new Promise(s=>setTimeout(s,1e3*Math.pow(2,i-1)))),yield this.sendSms("+1"+e,r)}catch(s){t=s,console.warn(`SMS sending attempt ${i+1}/${o+1} failed:`,s)}throw t||new Error("Failed to send SMS after multiple attempts")})}sendRideStatusUpdateNotifications(e,r){return c(this,null,function*(){try{if(!e.rider_id||!e.driver_id){console.warn("Ride is missing rider or driver ID. Status update notification skipped.");return}let[o,t]=yield Promise.all([this.userService.getUserById(e.rider_id),this.userService.getUserById(e.driver_id)]);if(!o||!t)throw new Error("Could not find rider or driver information");if(!o.phone||!t.phone){console.warn("Phone number missing for rider or driver. Status update notification skipped.");return}let i="",s="";switch(r){case"in-progress":i=`Your ride has started. Your driver ${t.full_name||"is"} on the way to ${e.dropoff_location}.`,s=`You have started the ride with ${o.full_name||"your rider"}. Destination: ${e.dropoff_location}.`;break;case"completed":i=`Your ride to ${e.dropoff_location} has been completed. Thank you for using Holy Rides!`,s=`You have completed the ride to ${e.dropoff_location}. Thank you for your service!`;break;case"canceled":i="Your ride has been canceled. Please contact support if you did not request this cancellation.",s=`The ride to ${e.dropoff_location} has been canceled. Please check your dashboard for new ride opportunities.`;break;default:return}let n=yield Promise.allSettled([this.sendSmsWithRetry(o.phone,i),this.sendSmsWithRetry(t.phone,s)]),[a,l]=n;a.status==="rejected"&&console.error("Failed to send status update SMS to rider:",a.reason),l.status==="rejected"&&console.error("Failed to send status update SMS to driver:",l.reason),a.status==="fulfilled"&&l.status==="fulfilled"&&console.log(`Ride status update (${r}) notifications sent successfully to both rider and driver`)}catch(o){console.error("Error sending ride status update notifications:",o)}})}sendRideBookingConfirmation(e){return c(this,null,function*(){try{let r=yield this.userService.getUserById(e.rider_id);if(!r||!r.phone){console.warn("Rider not found or phone number missing. Booking confirmation SMS skipped.");return}let o=new Date(e.pickup_time),i=o.getTime()<=Date.now()+30*60*1e3?"ASAP":o.toLocaleString(),s=o.toLocaleDateString(),n=e.fare?`$${e.fare.toFixed(2)}`:"TBD",a=`Your ride has been booked!
Pickup: ${e.pickup_location}
Dropoff: ${e.dropoff_location}
Pick up date: ${s}
Pickup Time: ${i}
Fare Estimate: ${n}
Driver will be assigned shortly. You'll receive updates as your ride details are confirmed.
Msg & data rates may apply. Message frequency varies.
Reply STOP to unsubscribe. View our policy at: https://bookholyrides.com/?p=1163`;yield this.sendSmsWithRetry(r.phone,a),console.log("Ride booking confirmation sent to rider")}catch(r){console.error("Error sending ride booking confirmation:",r)}})}sendRideBookingNotificationToDrivers(e){return c(this,null,function*(){try{let o=(yield this.userService.getUsersByRole("driver")).filter(a=>a.is_approved&&a.phone);if(o.length===0){console.warn("No approved drivers with phone numbers found. Driver notification skipped.");return}let t="A new trip has been booked! Login now at https://app.bookholyrides.com to view the ride details in the Holy Rides app.",i=yield Promise.allSettled(o.map(a=>this.sendSmsWithRetry(a.phone,t))),s=i.filter(a=>a.status==="fulfilled").length,n=i.filter(a=>a.status==="rejected").length;console.log(`Ride booking notification sent to ${s} drivers, ${n} failed`)}catch(r){console.error("Error sending ride booking notifications to drivers:",r)}})}sendEnhancedDriverAssignedNotification(e,r){return c(this,null,function*(){try{let o=yield this.userService.getUserById(e.rider_id);if(!o||!o.phone){console.warn("Rider not found or phone number missing. Driver assigned notification skipped.");return}let t=r.vehicle_info||"Vehicle details will be provided",i=r.license_number||"License details will be provided",n=`Great news \u2014 a driver has been assigned to your trip! \u{1F698}
Driver Name: ${r.full_name||"Driver"}
Vehicle: ${t}
License Number: ${i}
ETA: ETA will be calculated
Msg & data rates may apply. Reply STOP to unsubscribe.`;yield this.sendSmsWithRetry(o.phone,n),console.log("Enhanced driver assigned notification sent to rider")}catch(o){console.error("Error sending enhanced driver assigned notification:",o)}})}sendRideCancellationNotifications(e){return c(this,null,function*(){try{let r=[],o=yield this.userService.getUserById(e.rider_id);if(o&&o.phone){let t=`Your ride from ${e.pickup_location} to ${e.dropoff_location} has been cancelled. You can book a new ride at https://app.bookholyrides.com
Msg & data rates may apply. Reply STOP to unsubscribe.`;r.push(this.sendSmsWithRetry(o.phone,t))}if(e.driver_id){let t=yield this.userService.getUserById(e.driver_id);if(t&&t.phone){let i=`The ride to ${e.dropoff_location} has been cancelled. Please check your dashboard for new ride opportunities at https://app.bookholyrides.com
Msg & data rates may apply. Reply STOP to unsubscribe.`;r.push(this.sendSmsWithRetry(t.phone,i))}}if(r.length>0){let t=yield Promise.allSettled(r),i=t.filter(n=>n.status==="fulfilled").length,s=t.filter(n=>n.status==="rejected").length;console.log(`Ride cancellation notifications: ${i} sent, ${s} failed`)}}catch(r){console.error("Error sending ride cancellation notifications:",r)}})}sendRideCompletionNotification(e){return c(this,null,function*(){try{let r=yield this.userService.getUserById(e.rider_id);if(!r||!r.phone){console.warn("Rider not found or phone number missing. Ride completion notification skipped.");return}let t=`Your ride is complete! \u{1F698}
Thanks for riding with Holy Rides. Please complete payment now in the amount of ${e.fare?`$${e.fare.toFixed(2)}`:"$0.00"} using one of the following methods:
Cash app: https://cash.app/$HolyRides24
Square: https://square.link/u/o9zzuiAv?src=sheet
Msg & data rates may apply. Reply STOP to unsubscribe.`;yield this.sendSmsWithRetry(r.phone,t),console.log("Ride completion notification sent to rider")}catch(r){console.error("Error sending ride completion notification:",r)}})}sendPaymentConfirmationNotification(e,r){return c(this,null,function*(){try{let o=yield this.userService.getUserById(e.rider_id);if(!o||!o.phone){console.warn("Rider not found or phone number missing. Payment confirmation notification skipped.");return}let t=new Date(e.pickup_time),s=t.getTime()<=Date.now()+30*60*1e3?"ASAP":t.toLocaleString(),n=t.toLocaleDateString(),l=`Thank you! \u{1F389}
We've received your payment of ${`$${r.toFixed(2)}`} for your recent ride.
Pickup: ${e.pickup_location}
Dropoff: ${e.dropoff_location}
Pick up date: ${n}
Pickup Time: ${s}

Thanks for riding with Holy Rides. See you next time!
Msg & data rates may apply. Reply STOP to unsubscribe.`;yield this.sendSmsWithRetry(o.phone,l),console.log("Payment confirmation notification sent to rider")}catch(o){console.error("Error sending payment confirmation notification:",o)}})}sendDriverCancellationNotification(e){return c(this,null,function*(){try{let r=yield this.userService.getUserById(e.rider_id);if(r&&r.phone){let o=`Your assigned driver has cancelled. Don't worry - your ride from ${e.pickup_location} to ${e.dropoff_location} is still active and we're finding you another driver. You'll receive updates as soon as a new driver is assigned.
Msg & data rates may apply. Reply STOP to unsubscribe.`;yield this.sendSmsWithRetry(r.phone,o),console.log("Driver cancellation notification sent to rider")}}catch(r){console.error("Error sending driver cancellation notification:",r)}})}sendAdminRideStatusNotification(e,r){return c(this,null,function*(){try{let o="7272652963",[t,i]=yield Promise.all([e.driver_id?this.userService.getUserById(e.driver_id):null,this.userService.getUserById(e.rider_id)]),s=new Date(e.pickup_time).toLocaleString(),n="";if(r==="in-progress")n="STARTED";else if(r==="completed")n="COMPLETED";else return;let a=t?.full_name||"Unknown Driver",l=i?.full_name||"Unknown Rider",S=`\u{1F698} RIDE ${n}
Driver: ${a}
Rider: ${l}
From: ${e.pickup_location}
To: ${e.dropoff_location}
Pickup Time: ${s}
${e.fare?`Fare: $${e.fare.toFixed(2)}`:""}`;yield this.sendSmsWithRetry(o,S),console.log(`Admin notification sent for ride ${n.toLowerCase()}`)}catch(o){console.error("Error sending admin ride status notification:",o)}})}formatPhoneNumber(e){return e.startsWith("+")?e:e.match(/^[1-9][0-9]{1,3}[0-9]{5,12}$/)?`+${e}`:`+1${e.replace(/\D/g,"")}`}static \u0275fac=function(r){return new(r||d)(u(h),u(f))};static \u0275prov=p({token:d,factory:d.\u0275fac,providedIn:"root"})};export{h as a,y as b};

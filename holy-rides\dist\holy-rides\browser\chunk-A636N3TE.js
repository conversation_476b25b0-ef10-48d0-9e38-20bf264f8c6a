import"./chunk-MS4AQ6UA.js";import{b as ie}from"./chunk-IT7B5FSQ.js";import{a as W,c as z,d as Y}from"./chunk-3MEMYPGR.js";import"./chunk-EO3PEVGH.js";import"./chunk-3VEHVC57.js";import"./chunk-Q34CP4BD.js";import{c as R,d as I}from"./chunk-3EEDYH74.js";import{C as $,F as H,H as J,I as K,J as Q,b as N,d as p,f as k,g as G,k as B,n as j,o as D,s as L,u as V,w as A,x as U}from"./chunk-AG3SD6JT.js";import{Fc as O,Gd as q,Jd as T,Kb as n,La as a,Lb as b,Ld as X,Mb as S,Md as Z,Nd as ee,Pa as u,Qd as te,Rd as re,Wa as F,ab as s,hb as m,qb as o,rb as e,sb as c,uc as w,vc as E,yb as x,zb as P}from"./chunk-ST4QC4E3.js";import"./chunk-X5YLR3NI.js";import{i as y}from"./chunk-ODN5LVDJ.js";function ne(t,r){t&1&&(o(0,"mat-error"),n(1,"Full name is required"),e())}function ae(t,r){t&1&&(o(0,"mat-error"),n(1,"Email is required"),e())}function le(t,r){t&1&&(o(0,"mat-error"),n(1,"Please enter a valid email"),e())}function me(t,r){t&1&&(o(0,"mat-error"),n(1,"Password is required"),e())}function se(t,r){t&1&&(o(0,"mat-error"),n(1,"Password must be at least 6 characters"),e())}function pe(t,r){t&1&&(o(0,"mat-error"),n(1,"Password confirmation is required"),e())}function de(t,r){t&1&&(o(0,"mat-error"),n(1,"Passwords do not match"),e())}function ce(t,r){t&1&&(o(0,"mat-error"),n(1,"Phone number is required"),e())}function ue(t,r){if(t&1&&(o(0,"mat-option",16),n(1),e()),t&2){let l=r.$implicit;m("value",l.value),a(),S(" ",l.label," ")}}function ge(t,r){t&1&&(o(0,"mat-error"),n(1,"Role is required"),e())}function fe(t,r){if(t&1&&(o(0,"div",17),n(1),e()),t&2){let l=P();a(),b(l.error)}}var oe=class t{constructor(r,l,i,d){this.formBuilder=r;this.router=l;this.authService=i;this.smsService=d;this.registerForm=this.formBuilder.group({full_name:["",[p.required]],email:["",[p.required,p.email]],password:["",[p.required,p.minLength(6)]],confirmPassword:["",[p.required]],phone:["",[p.required]],role:["",[p.required]]},{validators:this.passwordMatchValidator})}registerForm;error="";loading=!1;roles=[{value:"rider",label:"Rider"},{value:"driver",label:"Driver"}];passwordMatchValidator(r){return r.get("password")?.value===r.get("confirmPassword")?.value?null:{mismatch:!0}}getWelcomeMessage(r){return`Welcome to the Holy Rides Transportation Family!
Thank you for signing up. You've opted in to receive text messages for important ride updates, promotions, and account notifications. ${r==="driver"?"Go ahead and log in now to start accepting rides at https://app.bookholyrides.com":"Go ahead and log in now to book your first ride at https://app.bookholyrides.com"}
Msg & data rates may apply. Message frequency varies.
To stop receiving messages, reply STOP. For help, reply HELP.
View our policy at: https://bookholyrides.com/?p=1163`}sendWelcomeSms(r,l){return y(this,null,function*(){try{let i=this.getWelcomeMessage(l);yield this.smsService.sendSms(r,i),console.log("Welcome SMS sent successfully")}catch(i){console.error("Failed to send welcome SMS:",i)}})}onSubmit(){return y(this,null,function*(){if(!this.registerForm.invalid){this.loading=!0,this.error="";try{let{error:r}=yield this.authService.register(this.registerForm.value.email,this.registerForm.value.password,this.registerForm.value.role,this.registerForm.value.phone,this.registerForm.value.full_name);if(r){this.error=r.message;return}yield new Promise(i=>setTimeout(i,1e3)),this.registerForm.value.phone&&(yield this.sendWelcomeSms(this.registerForm.value.phone,this.registerForm.value.role));let{error:l}=yield this.authService.login(this.registerForm.value.email,this.registerForm.value.password);if(l){this.error=l.message;return}yield this.router.navigate(["/auth/profile"])}catch(r){this.error=r.message}finally{this.loading=!1}}})}static \u0275fac=function(l){return new(l||t)(u(L),u(R),u(Q),u(ie))};static \u0275cmp=F({type:t,selectors:[["app-register"]],decls:48,vars:14,consts:[[1,"register-container"],[3,"ngSubmit","formGroup"],["appearance","outline"],["matInput","","type","text","formControlName","full_name","placeholder","Enter your full name"],[4,"ngIf"],["matInput","","type","email","formControlName","email","placeholder","Enter your email"],["matInput","","type","password","formControlName","password","placeholder","Enter your password"],["matInput","","type","password","formControlName","confirmPassword","placeholder","Confirm your password"],["matInput","","type","tel","formControlName","phone","placeholder","Enter your phone number"],["formControlName","role"],[3,"value",4,"ngFor","ngForOf"],["class","error-message",4,"ngIf"],[1,"button-container"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[1,"links"],["routerLink","/auth/login"],[3,"value"],[1,"error-message"]],template:function(l,i){if(l&1&&(o(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),n(4,"Register"),e()(),o(5,"mat-card-content")(6,"form",1),x("ngSubmit",function(){return i.onSubmit()}),o(7,"mat-form-field",2)(8,"mat-label"),n(9,"Full Name"),e(),c(10,"input",3),s(11,ne,2,0,"mat-error",4),e(),o(12,"mat-form-field",2)(13,"mat-label"),n(14,"Email"),e(),c(15,"input",5),s(16,ae,2,0,"mat-error",4)(17,le,2,0,"mat-error",4),e(),o(18,"mat-form-field",2)(19,"mat-label"),n(20,"Password"),e(),c(21,"input",6),s(22,me,2,0,"mat-error",4)(23,se,2,0,"mat-error",4),e(),o(24,"mat-form-field",2)(25,"mat-label"),n(26,"Confirm Password"),e(),c(27,"input",7),s(28,pe,2,0,"mat-error",4)(29,de,2,0,"mat-error",4),e(),o(30,"mat-form-field",2)(31,"mat-label"),n(32,"Phone Number"),e(),c(33,"input",8),s(34,ce,2,0,"mat-error",4),e(),o(35,"mat-form-field",2)(36,"mat-label"),n(37,"Role"),e(),o(38,"mat-select",9),s(39,ue,2,2,"mat-option",10),e(),s(40,ge,2,0,"mat-error",4),e(),s(41,fe,2,1,"div",11),o(42,"div",12)(43,"button",13),n(44),e()(),o(45,"div",14)(46,"a",15),n(47,"Already have an account? Login"),e()()()()()()),l&2){let d,g,f,_,h,v,M,C;a(6),m("formGroup",i.registerForm),a(5),m("ngIf",(d=i.registerForm.get("full_name"))==null||d.errors==null?null:d.errors.required),a(5),m("ngIf",(g=i.registerForm.get("email"))==null||g.errors==null?null:g.errors.required),a(),m("ngIf",(f=i.registerForm.get("email"))==null||f.errors==null?null:f.errors.email),a(5),m("ngIf",(_=i.registerForm.get("password"))==null||_.errors==null?null:_.errors.required),a(),m("ngIf",(h=i.registerForm.get("password"))==null||h.errors==null?null:h.errors.minlength),a(5),m("ngIf",(v=i.registerForm.get("confirmPassword"))==null||v.errors==null?null:v.errors.required),a(),m("ngIf",i.registerForm.errors==null?null:i.registerForm.errors.mismatch),a(5),m("ngIf",(M=i.registerForm.get("phone"))==null||M.errors==null?null:M.errors.required),a(5),m("ngForOf",i.roles),a(),m("ngIf",(C=i.registerForm.get("role"))==null||C.errors==null?null:C.errors.required),a(),m("ngIf",i.error),a(2),m("disabled",i.registerForm.invalid||i.loading),a(),S(" ",i.loading?"Registering...":"Register"," ")}},dependencies:[O,w,E,V,B,N,k,G,j,D,H,$,A,U,K,J,T,q,re,X,ee,te,Z,Y,z,W,I],styles:[".register-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;min-height:100vh;padding:20px}.register-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;margin-bottom:30px}.register-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{width:100px;height:100px;margin-bottom:10px}.register-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .app-name[_ngcontent-%COMP%]{font-size:24px;font-weight:500;color:#3f51b5;margin:0}.register-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]{width:100%;max-width:400px;padding:20px}.register-container[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.register-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]{margin-top:24px;display:flex;justify-content:center}.register-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;padding:8px}.register-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]{margin-top:16px;text-align:center}.register-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}.register-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.register-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#f44336;text-align:center;margin:8px 0}"]})};export{oe as RegisterComponent};

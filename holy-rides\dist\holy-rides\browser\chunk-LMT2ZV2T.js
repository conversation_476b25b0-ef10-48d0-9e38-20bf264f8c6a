import{w as ut,x as ht}from"./chunk-HEB4POL5.js";import{a as ct,c as mt,d as gt}from"./chunk-3MEMYPGR.js";import{C as pt}from"./chunk-AG3SD6JT.js";import{Ab as Q,Ad as rt,Ba as U,Bb as X,Bd as st,Ea as $,Ed as lt,Jb as tt,Jd as dt,Kb as D,La as r,Lb as et,Mb as L,Pa as Z,Wa as A,Wc as it,Xa as k,Ya as q,Yc as at,Z as w,_ as z,aa as M,ab as _,da as l,dc as B,ea as I,f as v,fa as T,fc as g,gb as c,gc as P,h as C,hb as u,hd as nt,jb as N,ka as j,la as S,ma as x,mb as h,na as f,nb as K,nd as ot,oa as Y,ob as W,pb as J,qb as n,rb as s,sb as b,va as O,wb as E,yb as p,z as V,za as G,zb as m}from"./chunk-ST4QC4E3.js";var bt=["mat-sort-header",""],yt=["*"];function vt(i,d){i&1&&(n(0,"div",2),f(),n(1,"svg",3),b(2,"path",4),s()())}var ft=new M("MAT_SORT_DEFAULT_OPTIONS"),St=(()=>{class i{_defaultOptions;_initializedStream=new C(1);sortables=new Map;_stateChanges=new v;active;start="asc";get direction(){return this._direction}set direction(t){this._direction=t}_direction="";disableClear;disabled=!1;sortChange=new O;initialized=this._initializedStream;constructor(t){this._defaultOptions=t}register(t){this.sortables.set(t.id,t)}deregister(t){this.sortables.delete(t.id)}sort(t){this.active!=t.id?(this.active=t.id,this.direction=t.start?t.start:this.start):this.direction=this.getNextSortDirection(t),this.sortChange.emit({active:this.active,direction:this.direction})}getNextSortDirection(t){if(!t)return"";let a=t?.disableClear??this.disableClear??!!this._defaultOptions?.disableClear,e=xt(t.start||this.start,a),o=e.indexOf(this.direction)+1;return o>=e.length&&(o=0),e[o]}ngOnInit(){this._initializedStream.next()}ngOnChanges(){this._stateChanges.next()}ngOnDestroy(){this._stateChanges.complete(),this._initializedStream.complete()}static \u0275fac=function(a){return new(a||i)(Z(ft,8))};static \u0275dir=q({type:i,selectors:[["","matSort",""]],hostAttrs:[1,"mat-sort"],inputs:{active:[0,"matSortActive","active"],start:[0,"matSortStart","start"],direction:[0,"matSortDirection","direction"],disableClear:[2,"matSortDisableClear","disableClear",g],disabled:[2,"matSortDisabled","disabled",g]},outputs:{sortChange:"matSortChange"},exportAs:["matSort"],features:[j]})}return i})();function xt(i,d){let t=["asc","desc"];return i=="desc"&&t.reverse(),d||t.push(""),t}var R=(()=>{class i{changes=new v;static \u0275fac=function(a){return new(a||i)};static \u0275prov=w({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();function Dt(i){return i||new R}var Pt={provide:R,deps:[[new I,new T,R]],useFactory:Dt},Xt=(()=>{class i{_intl=l(R);_sort=l(St,{optional:!0});_columnDef=l("MAT_SORT_HEADER_COLUMN_DEF",{optional:!0});_changeDetectorRef=l(B);_focusMonitor=l(it);_elementRef=l(G);_ariaDescriber=l(ot,{optional:!0});_renderChanges;_animationModule=l($,{optional:!0});_recentlyCleared=U(null);_sortButton;id;arrowPosition="after";start;disabled=!1;get sortActionDescription(){return this._sortActionDescription}set sortActionDescription(t){this._updateSortActionDescription(t)}_sortActionDescription="Sort";disableClear;constructor(){l(at).load(rt);let t=l(ft,{optional:!0});this._sort,t?.arrowPosition&&(this.arrowPosition=t?.arrowPosition)}ngOnInit(){!this.id&&this._columnDef&&(this.id=this._columnDef.name),this._sort.register(this),this._renderChanges=V(this._sort._stateChanges,this._sort.sortChange).subscribe(()=>this._changeDetectorRef.markForCheck()),this._sortButton=this._elementRef.nativeElement.querySelector(".mat-sort-header-container"),this._updateSortActionDescription(this._sortActionDescription)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(()=>this._recentlyCleared.set(null))}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._sort.deregister(this),this._renderChanges?.unsubscribe(),this._sortButton&&this._ariaDescriber?.removeDescription(this._sortButton,this._sortActionDescription)}_toggleOnInteraction(){if(!this._isDisabled()){let t=this._isSorted(),a=this._sort.direction;this._sort.sort(this),this._recentlyCleared.set(t&&!this._isSorted()?a:null)}}_handleKeydown(t){(t.keyCode===32||t.keyCode===13)&&(t.preventDefault(),this._toggleOnInteraction())}_isSorted(){return this._sort.active==this.id&&(this._sort.direction==="asc"||this._sort.direction==="desc")}_isDisabled(){return this._sort.disabled||this.disabled}_getAriaSortAttribute(){return this._isSorted()?this._sort.direction=="asc"?"ascending":"descending":"none"}_renderArrow(){return!this._isDisabled()||this._isSorted()}_updateSortActionDescription(t){this._sortButton&&(this._ariaDescriber?.removeDescription(this._sortButton,this._sortActionDescription),this._ariaDescriber?.describe(this._sortButton,t)),this._sortActionDescription=t}static \u0275fac=function(a){return new(a||i)};static \u0275cmp=A({type:i,selectors:[["","mat-sort-header",""]],hostAttrs:[1,"mat-sort-header"],hostVars:3,hostBindings:function(a,e){a&1&&p("click",function(){return e._toggleOnInteraction()})("keydown",function(y){return e._handleKeydown(y)})("mouseleave",function(){return e._recentlyCleared.set(null)}),a&2&&(c("aria-sort",e._getAriaSortAttribute()),N("mat-sort-header-disabled",e._isDisabled()))},inputs:{id:[0,"mat-sort-header","id"],arrowPosition:"arrowPosition",start:"start",disabled:[2,"disabled","disabled",g],sortActionDescription:"sortActionDescription",disableClear:[2,"disableClear","disableClear",g]},exportAs:["matSortHeader"],attrs:bt,ngContentSelectors:yt,decls:4,vars:17,consts:[[1,"mat-sort-header-container","mat-focus-indicator"],[1,"mat-sort-header-content"],[1,"mat-sort-header-arrow"],["viewBox","0 -960 960 960","focusable","false","aria-hidden","true"],["d","M440-240v-368L296-464l-56-56 240-240 240 240-56 56-144-144v368h-80Z"]],template:function(a,e){a&1&&(Q(),n(0,"div",0)(1,"div",1),X(2),s(),_(3,vt,3,0,"div",2),s()),a&2&&(N("mat-sort-header-sorted",e._isSorted())("mat-sort-header-position-before",e.arrowPosition==="before")("mat-sort-header-descending",e._sort.direction==="desc")("mat-sort-header-ascending",e._sort.direction==="asc")("mat-sort-header-recently-cleared-ascending",e._recentlyCleared()==="asc")("mat-sort-header-recently-cleared-descending",e._recentlyCleared()==="desc")("mat-sort-header-animations-disabled",e._animationModule==="NoopAnimations"),c("tabindex",e._isDisabled()?null:0)("role",e._isDisabled()?null:"button"),r(3),h(e._renderArrow()?3:-1))},styles:[`.mat-sort-header{cursor:pointer}.mat-sort-header-disabled{cursor:default}.mat-sort-header-container{display:flex;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}@keyframes _mat-sort-header-recently-cleared-ascending{from{transform:translateY(0);opacity:1}to{transform:translateY(-25%);opacity:0}}@keyframes _mat-sort-header-recently-cleared-descending{from{transform:translateY(0) rotate(180deg);opacity:1}to{transform:translateY(25%) rotate(180deg);opacity:0}}.mat-sort-header-arrow{height:12px;width:12px;position:relative;transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1),opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);opacity:0;overflow:visible;color:var(--mat-sort-arrow-color, var(--mat-sys-on-surface))}.mat-sort-header.cdk-keyboard-focused .mat-sort-header-arrow,.mat-sort-header.cdk-program-focused .mat-sort-header-arrow,.mat-sort-header:hover .mat-sort-header-arrow{opacity:.54}.mat-sort-header .mat-sort-header-sorted .mat-sort-header-arrow{opacity:1}.mat-sort-header-descending .mat-sort-header-arrow{transform:rotate(180deg)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transform:translateY(-25%)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-ascending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-recently-cleared-descending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-descending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-animations-disabled .mat-sort-header-arrow{transition-duration:0ms;animation-duration:0ms}.mat-sort-header-arrow svg{width:24px;height:24px;fill:currentColor;position:absolute;top:50%;left:50%;margin:-12px 0 0 -12px;transform:translateZ(0)}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}
`],encapsulation:2,changeDetection:0})}return i})(),te=(()=>{class i{static \u0275fac=function(a){return new(a||i)};static \u0275mod=k({type:i});static \u0275inj=z({providers:[Pt],imports:[lt]})}return i})();function Ct(i,d){if(i&1&&(n(0,"mat-option",17),D(1),s()),i&2){let t=d.$implicit;u("value",t),r(),L(" ",t," ")}}function wt(i,d){if(i&1){let t=E();n(0,"mat-form-field",14)(1,"mat-select",16,0),p("selectionChange",function(e){S(t);let o=m(2);return x(o._changePageSize(e.value))}),W(3,Ct,2,2,"mat-option",17,K),s(),n(5,"div",18),p("click",function(){S(t);let e=tt(2);return x(e.open())}),s()()}if(i&2){let t=m(2);u("appearance",t._formFieldAppearance)("color",t.color),r(),u("value",t.pageSize)("disabled",t.disabled)("aria-labelledby",t._pageSizeLabelId)("panelClass",t.selectConfig.panelClass||"")("disableOptionCentering",t.selectConfig.disableOptionCentering),r(2),J(t._displayedPageSizeOptions)}}function zt(i,d){if(i&1&&(n(0,"div",15),D(1),s()),i&2){let t=m(2);r(),et(t.pageSize)}}function Mt(i,d){if(i&1&&(n(0,"div",3)(1,"div",13),D(2),s(),_(3,wt,6,7,"mat-form-field",14)(4,zt,2,1,"div",15),s()),i&2){let t=m();r(),c("id",t._pageSizeLabelId),r(),L(" ",t._intl.itemsPerPageLabel," "),r(),h(t._displayedPageSizeOptions.length>1?3:-1),r(),h(t._displayedPageSizeOptions.length<=1?4:-1)}}function It(i,d){if(i&1){let t=E();n(0,"button",19),p("click",function(){S(t);let e=m();return x(e._buttonClicked(0,e._previousButtonsDisabled()))}),f(),n(1,"svg",8),b(2,"path",20),s()()}if(i&2){let t=m();u("matTooltip",t._intl.firstPageLabel)("matTooltipDisabled",t._previousButtonsDisabled())("disabled",t._previousButtonsDisabled())("tabindex",t._previousButtonsDisabled()?-1:null),c("aria-label",t._intl.firstPageLabel)}}function Tt(i,d){if(i&1){let t=E();n(0,"button",21),p("click",function(){S(t);let e=m();return x(e._buttonClicked(e.getNumberOfPages()-1,e._nextButtonsDisabled()))}),f(),n(1,"svg",8),b(2,"path",22),s()()}if(i&2){let t=m();u("matTooltip",t._intl.lastPageLabel)("matTooltipDisabled",t._nextButtonsDisabled())("disabled",t._nextButtonsDisabled())("tabindex",t._nextButtonsDisabled()?-1:null),c("aria-label",t._intl.lastPageLabel)}}var F=(()=>{class i{changes=new v;itemsPerPageLabel="Items per page:";nextPageLabel="Next page";previousPageLabel="Previous page";firstPageLabel="First page";lastPageLabel="Last page";getRangeLabel=(t,a,e)=>{if(e==0||a==0)return`0 of ${e}`;e=Math.max(e,0);let o=t*a,y=o<e?Math.min(o+a,e):o+a;return`${o+1} \u2013 ${y} of ${e}`};static \u0275fac=function(a){return new(a||i)};static \u0275prov=w({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();function Ot(i){return i||new F}var At={provide:F,deps:[[new I,new T,F]],useFactory:Ot},kt=50;var Et=new M("MAT_PAGINATOR_DEFAULT_OPTIONS"),Lt=(()=>{class i{_intl=l(F);_changeDetectorRef=l(B);_formFieldAppearance;_pageSizeLabelId=l(nt).getId("mat-paginator-page-size-label-");_intlChanges;_isInitialized=!1;_initializedStream=new C(1);color;get pageIndex(){return this._pageIndex}set pageIndex(t){this._pageIndex=Math.max(t||0,0),this._changeDetectorRef.markForCheck()}_pageIndex=0;get length(){return this._length}set length(t){this._length=t||0,this._changeDetectorRef.markForCheck()}_length=0;get pageSize(){return this._pageSize}set pageSize(t){this._pageSize=Math.max(t||0,0),this._updateDisplayedPageSizeOptions()}_pageSize;get pageSizeOptions(){return this._pageSizeOptions}set pageSizeOptions(t){this._pageSizeOptions=(t||[]).map(a=>P(a,0)),this._updateDisplayedPageSizeOptions()}_pageSizeOptions=[];hidePageSize=!1;showFirstLastButtons=!1;selectConfig={};disabled=!1;page=new O;_displayedPageSizeOptions;initialized=this._initializedStream;constructor(){let t=this._intl,a=l(Et,{optional:!0});if(this._intlChanges=t.changes.subscribe(()=>this._changeDetectorRef.markForCheck()),a){let{pageSize:e,pageSizeOptions:o,hidePageSize:y,showFirstLastButtons:H}=a;e!=null&&(this._pageSize=e),o!=null&&(this._pageSizeOptions=o),y!=null&&(this.hidePageSize=y),H!=null&&(this.showFirstLastButtons=H)}this._formFieldAppearance=a?.formFieldAppearance||"outline"}ngOnInit(){this._isInitialized=!0,this._updateDisplayedPageSizeOptions(),this._initializedStream.next()}ngOnDestroy(){this._initializedStream.complete(),this._intlChanges.unsubscribe()}nextPage(){this.hasNextPage()&&this._navigate(this.pageIndex+1)}previousPage(){this.hasPreviousPage()&&this._navigate(this.pageIndex-1)}firstPage(){this.hasPreviousPage()&&this._navigate(0)}lastPage(){this.hasNextPage()&&this._navigate(this.getNumberOfPages()-1)}hasPreviousPage(){return this.pageIndex>=1&&this.pageSize!=0}hasNextPage(){let t=this.getNumberOfPages()-1;return this.pageIndex<t&&this.pageSize!=0}getNumberOfPages(){return this.pageSize?Math.ceil(this.length/this.pageSize):0}_changePageSize(t){let a=this.pageIndex*this.pageSize,e=this.pageIndex;this.pageIndex=Math.floor(a/t)||0,this.pageSize=t,this._emitPageEvent(e)}_nextButtonsDisabled(){return this.disabled||!this.hasNextPage()}_previousButtonsDisabled(){return this.disabled||!this.hasPreviousPage()}_updateDisplayedPageSizeOptions(){this._isInitialized&&(this.pageSize||(this._pageSize=this.pageSizeOptions.length!=0?this.pageSizeOptions[0]:kt),this._displayedPageSizeOptions=this.pageSizeOptions.slice(),this._displayedPageSizeOptions.indexOf(this.pageSize)===-1&&this._displayedPageSizeOptions.push(this.pageSize),this._displayedPageSizeOptions.sort((t,a)=>t-a),this._changeDetectorRef.markForCheck())}_emitPageEvent(t){this.page.emit({previousPageIndex:t,pageIndex:this.pageIndex,pageSize:this.pageSize,length:this.length})}_navigate(t){let a=this.pageIndex;t!==a&&(this.pageIndex=t,this._emitPageEvent(a))}_buttonClicked(t,a){a||this._navigate(t)}static \u0275fac=function(a){return new(a||i)};static \u0275cmp=A({type:i,selectors:[["mat-paginator"]],hostAttrs:["role","group",1,"mat-mdc-paginator"],inputs:{color:"color",pageIndex:[2,"pageIndex","pageIndex",P],length:[2,"length","length",P],pageSize:[2,"pageSize","pageSize",P],pageSizeOptions:"pageSizeOptions",hidePageSize:[2,"hidePageSize","hidePageSize",g],showFirstLastButtons:[2,"showFirstLastButtons","showFirstLastButtons",g],selectConfig:"selectConfig",disabled:[2,"disabled","disabled",g]},outputs:{page:"page"},exportAs:["matPaginator"],decls:14,vars:14,consts:[["selectRef",""],[1,"mat-mdc-paginator-outer-container"],[1,"mat-mdc-paginator-container"],[1,"mat-mdc-paginator-page-size"],[1,"mat-mdc-paginator-range-actions"],["aria-live","polite",1,"mat-mdc-paginator-range-label"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-first",3,"matTooltip","matTooltipDisabled","disabled","tabindex"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-previous",3,"click","matTooltip","matTooltipDisabled","disabled","tabindex"],["viewBox","0 0 24 24","focusable","false","aria-hidden","true",1,"mat-mdc-paginator-icon"],["d","M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-next",3,"click","matTooltip","matTooltipDisabled","disabled","tabindex"],["d","M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-last",3,"matTooltip","matTooltipDisabled","disabled","tabindex"],[1,"mat-mdc-paginator-page-size-label"],[1,"mat-mdc-paginator-page-size-select",3,"appearance","color"],[1,"mat-mdc-paginator-page-size-value"],["hideSingleSelectionIndicator","",3,"selectionChange","value","disabled","aria-labelledby","panelClass","disableOptionCentering"],[3,"value"],[1,"mat-mdc-paginator-touch-target",3,"click"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-first",3,"click","matTooltip","matTooltipDisabled","disabled","tabindex"],["d","M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-last",3,"click","matTooltip","matTooltipDisabled","disabled","tabindex"],["d","M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"]],template:function(a,e){a&1&&(n(0,"div",1)(1,"div",2),_(2,Mt,5,4,"div",3),n(3,"div",4)(4,"div",5),D(5),s(),_(6,It,3,5,"button",6),n(7,"button",7),p("click",function(){return e._buttonClicked(e.pageIndex-1,e._previousButtonsDisabled())}),f(),n(8,"svg",8),b(9,"path",9),s()(),Y(),n(10,"button",10),p("click",function(){return e._buttonClicked(e.pageIndex+1,e._nextButtonsDisabled())}),f(),n(11,"svg",8),b(12,"path",11),s()(),_(13,Tt,3,5,"button",12),s()()()),a&2&&(r(2),h(e.hidePageSize?-1:2),r(3),L(" ",e._intl.getRangeLabel(e.pageIndex,e.pageSize,e.length)," "),r(),h(e.showFirstLastButtons?6:-1),r(),u("matTooltip",e._intl.previousPageLabel)("matTooltipDisabled",e._previousButtonsDisabled())("disabled",e._previousButtonsDisabled())("tabindex",e._previousButtonsDisabled()?-1:null),c("aria-label",e._intl.previousPageLabel),r(3),u("matTooltip",e._intl.nextPageLabel)("matTooltipDisabled",e._nextButtonsDisabled())("disabled",e._nextButtonsDisabled())("tabindex",e._nextButtonsDisabled()?-1:null),c("aria-label",e._intl.nextPageLabel),r(3),h(e.showFirstLastButtons?13:-1))},dependencies:[pt,mt,ct,st,ut],styles:[`.mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-sys-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-sys-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-sys-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-sys-body-small-tracking));--mat-form-field-container-height:var(--mat-paginator-form-field-container-height, 40px);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding, 8px)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-sys-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size, 56px)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}@media(forced-colors: active){.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon,.mat-mdc-paginator-icon{fill:currentColor}.mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-range-actions .mat-mdc-icon-button[aria-disabled]{color:GrayText}}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display, block);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}
`],encapsulation:2,changeDetection:0})}return i})(),be=(()=>{class i{static \u0275fac=function(a){return new(a||i)};static \u0275mod=k({type:i});static \u0275inj=z({providers:[At],imports:[dt,gt,ht,Lt]})}return i})();export{St as a,Xt as b,te as c,Lt as d,be as e};

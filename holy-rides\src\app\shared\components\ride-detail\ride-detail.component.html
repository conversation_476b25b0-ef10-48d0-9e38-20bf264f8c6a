<mat-card *ngIf="ride">
  <mat-card-header>
    <mat-card-title>Ride Details</mat-card-title>
    <mat-card-subtitle>{{ formatDate(ride.created_at) }}</mat-card-subtitle>
    <button mat-icon-button class="close-button" (click)="onClose()">
      <mat-icon>close</mat-icon>
    </button>
  </mat-card-header>

  <mat-card-content>
    <div class="ride-details">
      <div class="detail-row">
        <span class="label">Status:</span>
        <span class="value status-badge" [ngClass]="'status-' + ride.status">
          {{ formatStatus(ride.status) }}
        </span>
      </div>

      <div class="detail-row">
        <span class="label">Pickup:</span>
        <span class="value">{{ ride.pickup_location }}</span>
      </div>

      <div class="detail-row">
        <span class="label">Destination:</span>
        <span class="value">{{ ride.dropoff_location }}</span>
      </div>
      <div class="detail-row">
        <span class="label">Rider:</span>
        <span class="value">{{ getUserName(ride.rider_id) }}</span>
      </div>

      <div class="detail-row">
        <span class="label">Driver:</span>
        <span class="value">{{ ride.driver_id ? getUserName(ride.driver_id) : 'No driver assigned' }}</span>
      </div>
      <div class="detail-row">
        <span class="label">Pickup Time:</span>
        <span class="value">{{ formatDate(ride.pickup_time) }}</span>
      </div>

      <!-- Fare display for admin and rider users, but not for driver -->
      <div class="detail-row" *ngIf="ride.fare && (isAdmin || isRider) && !isDriver" style="display: flex; align-items: center;">
        <span class="label">Fare:</span>
        <span class="value">${{ ride.fare }}</span>
      </div>

      <div class="detail-row" *ngIf="ride.payment_status">
        <span class="label">Payment Status:</span>
        <span class="value status-badge" [ngClass]="'payment-' + ride.payment_status">
          {{ ride.payment_status }}
        </span>
      </div>

      <!-- Payment status update button for admin users -->
      <div class="detail-row" *ngIf="isAdmin && ride.status === 'completed' && (!ride.payment_status || ride.payment_status !== 'completed')" style="margin-top: 10px;">
        <button mat-raised-button color="primary" [disabled]="updatingPaymentStatus" (click)="markPaymentCompleted()">
          <mat-icon>check_circle</mat-icon>
          {{ updatingPaymentStatus ? 'Updating...' : 'Mark Payment Completed' }}
        </button>
      </div>
      </div>
      <!-- Fare input for admin users -->
      <div class="detail-row admin-fare-input" stlye="display: flex; align-items: start;" *ngIf="isAdmin && ride.status === 'requested'" >
        <span class="label">Fare:</span>
        <div class="admin-input-container" style="display: flex; align-items: start;">
          <mat-form-field appearance="outline" class="fare-input">
        <mat-label>Set Fare</mat-label>
        <input matInput type="number" step="0.01" min="0" [formControl]="fareControl" placeholder="Enter fare amount">
        <span matPrefix>$&nbsp;</span>
        <mat-error *ngIf="fareControl.hasError('required')">Fare is required</mat-error>
        <mat-error *ngIf="fareControl.hasError('min')">Fare must be greater than or equal to 0</mat-error>
        <mat-error *ngIf="fareControl.hasError('pattern')">Fare must be a valid number with up to 2 decimal places</mat-error>
          </mat-form-field>
          <button mat-raised-button color="primary" [disabled]="fareControl.invalid || updatingFare" (click)="updateFare()">
        <mat-icon>save</mat-icon>
        {{ updatingFare ? 'Saving...' : 'Save' }}
          </button>
        </div>
      </div>

      <div class="detail-row" *ngIf="ride.distance_miles">
        <span class="label">Distance:</span>
        <span class="value">{{ ride.distance_miles }} miles</span>
      </div>

      <div class="detail-row" *ngIf="ride.duration_minutes">
        <span class="label">Duration:</span>
        <span class="value">{{ ride.duration_minutes }} minutes</span>
      </div>

      <!-- Payment button for completed rides -->
      <!-- <div class="detail-row" *ngIf="isAdmin && ride.status === 'completed'">
        <button mat-raised-button color="primary" (click)="viewPayment(ride)">
          <mat-icon>payment</mat-icon>
          Pay Driver
        </button>
      </div> -->

    <mat-divider class="section-divider"></mat-divider>

    <mat-tab-group>
      <mat-tab label="Map">
        <div class="map-container" *ngIf="ride.pickup_location && ride.dropoff_location">
          <app-map-display
            [origin]="ride.pickup_location"
            [destination]="ride.dropoff_location">
          </app-map-display>
        </div>
      </mat-tab>

      <mat-tab label="Chat" *ngIf="ride.status !== 'requested' && otherUser">
        <app-ride-chat [rideId]="ride.id"></app-ride-chat>
      </mat-tab>

      <mat-tab label="Rate" *ngIf="canRate && otherUser">
        <div class="rating-container">
          <div *ngIf="!hasRated; else alreadyRated">
            <app-rating-form
              [ride]="ride"
              [userToRate]="otherUser"
              (ratingSubmitted)="onRatingSubmitted($event)"
              (ratingCancelled)="onRatingCancelled()">
            </app-rating-form>
          </div>

          <ng-template #alreadyRated>
            <div class="already-rated-message">
              <mat-icon color="primary">check_circle</mat-icon>
              <p>You've already rated this ride. Thank you for your feedback!</p>
            </div>
          </ng-template>
        </div>
      </mat-tab>

      <mat-tab label="Ratings" *ngIf="otherUser">
        <app-rating-display [userId]="otherUser.id"></app-rating-display>
      </mat-tab>
    </mat-tab-group>
  </mat-card-content>
</mat-card>

import{a as Ia,d as At,e as Ft,f as Ma}from"./chunk-YSBVITGM.js";import{a as Sa,b as Ta}from"./chunk-OFTCLERB.js";import{a as Qe,b as Ue}from"./chunk-WSXVBUWR.js";import{a as Pt,b as Ra}from"./chunk-IT7B5FSQ.js";import{b as St,c as Tt}from"./chunk-EO3PEVGH.js";import{a as yt,b as wt}from"./chunk-553Y2ERR.js";import{a as aa,b as _t,d as na,e as it,f as oa,g as ca,h as la,i as bt,j as da,k as ma,p as vt,s as ha}from"./chunk-3VEHVC57.js";import{a as ra,b as gt,c as sa,d as at}from"./chunk-Q34CP4BD.js";import{C as Rt,D as Da,F as It,H as Mt,I as Et,J as ue,a as pa,b as Ct,d as we,f as xt,g as ua,i as fa,k as _a,l as ga,m as ba,n as ni,o as va,p as ya,s as wa,u as kt,v as Ca,w as Dt,x as xa,y as ka}from"./chunk-AG3SD6JT.js";import{$b as qi,Ab as U,Ad as Pe,B as Te,Bb as S,Bc as Gi,Bd as pt,Ca as Fe,Cc as Wi,Cd as ye,Db as E,Ea as he,Eb as O,Ed as ee,F as Hi,Fb as _,Fc as Ve,Fd as ia,Gb as g,Gd as ut,H as Vi,Ha as pe,Jb as tt,Jd as ft,Kb as h,La as d,Lb as z,Ld as qe,Ma as G,Mb as J,Md as Ge,Nb as mt,Nd as We,Oa as et,Od as $e,Pa as W,Qc as Jt,Qd as Ye,R as zi,Ra as re,Rc as Ie,Rd as Ke,S as ne,Sb as M,Sc as $i,T as ct,Tc as ei,U as P,Uc as Yi,Wa as x,Wb as Zt,Wc as Me,Xa as X,Xb as Qi,Xc as Ki,Y as ji,Ya as f,Yb as Ui,Yc as Ee,Z as ke,Zc as Xi,_ as K,_a as R,a as le,aa as A,ab as w,ad as Zi,c as Li,ca as _e,da as s,dc as Y,ec as Be,f as N,fc as y,g as ie,gb as V,gc as be,gd as Ji,hb as p,hd as ve,i as ot,ib as Oe,id as ze,jb as T,k as xe,ka as de,kb as Le,kd as je,la as k,m as Bi,ma as D,mb as Q,mc as De,na as lt,nb as Yt,nd as ea,ob as Kt,pa as H,pb as Xt,q as rt,qb as c,r as st,ra as ge,rb as l,sa as Je,sb as B,tc as Ne,td as ti,uc as ht,va as C,vb as Z,vc as He,wa as oe,wb as $,x as Ni,xb as dt,xd as ii,yb as I,yd as ai,z as ae,za as F,zb as b,zd as ta}from"./chunk-ST4QC4E3.js";import{a as Se,b as Oi,i as v}from"./chunk-ODN5LVDJ.js";var Ot=class a{constructor(n){this.authService=n;this.supabase=n.supabase,this.loadActivePricing()}supabase;pricingSubject=new ie(null);pricing$=this.pricingSubject.asObservable();loadActivePricing(){return v(this,null,function*(){try{let{data:n,error:e}=yield this.supabase.from("ride_pricing").select("*").eq("is_active",!0).order("created_at",{ascending:!1}).limit(1).single();if(e)throw e;return this.pricingSubject.next(n),n}catch(n){return console.error("Error loading active pricing:",n),null}})}getAllPricing(){return v(this,null,function*(){try{let{data:n,error:e}=yield this.supabase.from("ride_pricing").select("*").order("created_at",{ascending:!1});if(e)throw e;return n||[]}catch(n){return console.error("Error loading pricing configurations:",n),[]}})}createPricing(n){return v(this,null,function*(){try{if(n.is_active){let{error:i}=yield this.supabase.from("ride_pricing").update({is_active:!1}).eq("is_active",!0);if(i)throw i}let{data:e,error:t}=yield this.supabase.from("ride_pricing").insert([n]).select().single();if(t)throw t;return n.is_active&&this.pricingSubject.next(e),e}catch(e){return console.error("Error creating pricing configuration:",e),null}})}updatePricing(n,e){return v(this,null,function*(){try{if(e.is_active){let{error:o}=yield this.supabase.from("ride_pricing").update({is_active:!1}).neq("id",n).eq("is_active",!0);if(o)throw o}let{data:t,error:i}=yield this.supabase.from("ride_pricing").update(e).eq("id",n).select().single();if(i)throw i;return t.is_active&&this.pricingSubject.next(t),t}catch(t){return console.error("Error updating pricing configuration:",t),null}})}setActiveStatus(n,e){return v(this,null,function*(){try{if(e){let{error:i}=yield this.supabase.from("ride_pricing").update({is_active:!1}).neq("id",n);if(i)throw i}let{error:t}=yield this.supabase.from("ride_pricing").update({is_active:e}).eq("id",n);if(t)throw t;return yield this.loadActivePricing(),!0}catch(t){return console.error("Error setting active status:",t),!1}})}deletePricing(n){return v(this,null,function*(){try{let{error:e}=yield this.supabase.from("ride_pricing").delete().eq("id",n);if(e)throw e;return yield this.loadActivePricing(),!0}catch(e){return console.error("Error deleting pricing configuration:",e),!1}})}static \u0275fac=function(e){return new(e||a)(_e(ue))};static \u0275prov=ke({token:a,factory:a.\u0275fac,providedIn:"root"})};var Bt=class a{constructor(n,e,t,i){this.authService=n;this.locationService=e;this.ridePricingService=t;this.smsService=i;this.supabase=n.supabase}supabase;payoutsSubject=new ie([]);payouts$=this.payoutsSubject.asObservable();calculateFare(n,e){return v(this,null,function*(){let t=yield this.ridePricingService.loadActivePricing();console.log(t);let i=t?.base_fare??5,o=t?.distance_rate??1.5,r=t?.time_rate??.25;return+(i+n*o+e*r)})}estimateFare(n,e){return v(this,null,function*(){try{let t=yield this.locationService.calculateRoute(n,e);console.log(t);let i=yield this.calculateFare(t.distance,t.duration);return console.log(i),{fare:i,routeInfo:t}}catch(t){console.error("Error estimating fare:",t);let i=10,o=20;return{fare:yield this.calculateFare(i,o),routeInfo:{distance:i,duration:o}}}})}createPaymentIntent(n,e){return v(this,null,function*(){yield new Promise(o=>setTimeout(o,800));let t=`sq_${Math.random().toString(36).substring(2,15)}`,i=`pmt_${Math.random().toString(36).substring(2,15)}`;return yield this.updateRidePaymentDetails(n,{payment_id:i,payment_status:"pending",amount:e}),{clientSecret:t,paymentId:i}})}processPayment(n,e){return v(this,null,function*(){try{yield new Promise(i=>setTimeout(i,1e3));let t=Math.random()>.1;if(t){yield this.updateRidePaymentStatus(n,"completed");let i=yield this.getRide(n);i&&i.driver_id&&i.amount&&(yield this.createDriverPayout(i.driver_id,n,i.amount,"amount"))}else yield this.updateRidePaymentStatus(n,"failed");return t}catch(t){return console.error("Error processing payment:",t),yield this.updateRidePaymentStatus(n,"failed"),!1}})}processRefund(n){return v(this,null,function*(){try{yield new Promise(t=>setTimeout(t,1e3));let e=Math.random()>.1;if(e){yield this.updateRidePaymentStatus(n,"refunded");let t=yield this.getRide(n);t&&t.driver_id&&(yield this.updateDriverPayoutStatus(t.driver_id,n,"failed"))}return e}catch(e){return console.error("Error processing refund:",e),!1}})}getRide(n){return v(this,null,function*(){try{let{data:e,error:t}=yield this.supabase.from("rides").select("*").eq("id",n).single();if(t)throw t;return e}catch(e){return console.error("Error fetching ride:",e),null}})}updateRidePaymentDetails(n,e){return v(this,null,function*(){try{let{error:t}=yield this.supabase.from("rides").update(Oi(Se({},e),{updated_at:new Date().toISOString()})).eq("id",n);if(t)throw t;return!0}catch(t){return console.error("Error updating ride payment details:",t),!1}})}updateRidePaymentStatus(n,e){return v(this,null,function*(){let t=yield this.updateRidePaymentDetails(n,{payment_status:e});return t&&(e==="paid"||e==="completed")&&setTimeout(()=>v(this,null,function*(){try{let i=yield this.getRide(n);i&&i.fare&&(yield this.smsService.sendPaymentConfirmationNotification(i,i.fare))}catch(i){console.error("Error sending payment confirmation notification:",i)}}),0),t})}createDriverPayout(n,e,t,i="amount",o){return v(this,null,function*(){try{let r={driver_id:n,ride_id:e,fare:t,status:"pending",payout_type:i};i==="percentage"&&o&&(r.percentage=o);let{error:m}=yield this.supabase.from("driver_payouts").insert([r]);if(m)throw m;return yield this.getDriverPayouts(n),!0}catch(r){return console.error("Error creating driver payout:",r),!1}})}updateDriverPayoutStatus(n,e,t){return v(this,null,function*(){try{let{error:i}=yield this.supabase.from("driver_payouts").update({status:t,updated_at:new Date().toISOString()}).eq("driver_id",n).eq("ride_id",e);if(i)throw i;return yield this.getDriverPayouts(n),!0}catch(i){return console.error("Error updating driver payout status:",i),!1}})}updateDriverPayoutAmount(n,e,t,i="amount",o){return v(this,null,function*(){try{let r={amount:t,payout_type:i,updated_at:new Date().toISOString()};i==="percentage"&&o&&(r.percentage=o);let{error:m}=yield this.supabase.from("driver_payouts").update(r).eq("driver_id",n).eq("ride_id",e);if(m)throw m;return yield this.getDriverPayouts(n),!0}catch(r){return console.error("Error updating driver payout amount:",r),!1}})}getDriverPayouts(n){return v(this,null,function*(){try{let{data:e,error:t}=yield this.supabase.from("driver_payouts").select("*").eq("driver_id",n).order("created_at",{ascending:!1});if(t)throw t;return this.payoutsSubject.next(e),e}catch(e){return console.error("Error fetching driver payouts:",e),[]}})}getDriverTotalEarnings(n){return v(this,null,function*(){try{let{data:e,error:t}=yield this.supabase.from("driver_payouts").select("amount").eq("driver_id",n).eq("status","paid");if(t)throw t;return e.reduce((i,o)=>i+o.amount,0)}catch(e){return console.error("Error calculating driver earnings:",e),0}})}getDriverPendingEarnings(n){return v(this,null,function*(){try{let{data:e,error:t}=yield this.supabase.from("driver_payouts").select("amount").eq("driver_id",n).eq("status","pending");if(t)throw t;return e.reduce((i,o)=>i+o.amount,0)}catch(e){return console.error("Error calculating pending earnings:",e),0}})}static \u0275fac=function(e){return new(e||a)(_e(ue),_e(Sa),_e(Ot),_e(Ra))};static \u0275prov=ke({token:a,factory:a.\u0275fac,providedIn:"root"})};var li=["*"];function vn(a,n){a&1&&S(0)}var yn=["tabListContainer"],wn=["tabList"],Cn=["tabListInner"],xn=["nextPaginator"],kn=["previousPaginator"],Dn=["content"];function Rn(a,n){}var Sn=["tabBodyWrapper"],Tn=["tabHeader"];function In(a,n){}function Mn(a,n){if(a&1&&w(0,In,0,0,"ng-template",12),a&2){let e=b().$implicit;p("cdkPortalOutlet",e.templateLabel)}}function En(a,n){if(a&1&&h(0),a&2){let e=b().$implicit;z(e.textLabel)}}function Pn(a,n){if(a&1){let e=$();c(0,"div",7,2),I("click",function(){let i=k(e),o=i.$implicit,r=i.$index,m=b(),u=tt(1);return D(m._handleClick(o,u,r))})("cdkFocusChange",function(i){let o=k(e).$index,r=b();return D(r._tabFocusChanged(i,o))}),B(2,"span",8)(3,"div",9),c(4,"span",10)(5,"span",11),w(6,Mn,1,1,null,12)(7,En,1,1),l()()()}if(a&2){let e=n.$implicit,t=n.$index,i=tt(1),o=b();Le(e.labelClass),T("mdc-tab--active",o.selectedIndex===t),p("id",o._getTabLabelId(e,t))("disabled",e.disabled)("fitInkBarToContent",o.fitInkBarToContent),V("tabIndex",o._getTabIndex(t))("aria-posinset",t+1)("aria-setsize",o._tabs.length)("aria-controls",o._getTabContentId(t))("aria-selected",o.selectedIndex===t)("aria-label",e.ariaLabel||null)("aria-labelledby",!e.ariaLabel&&e.ariaLabelledby?e.ariaLabelledby:null),d(3),p("matRippleTrigger",i)("matRippleDisabled",e.disabled||o.disableRipple),d(3),Q(e.templateLabel?6:7)}}function An(a,n){a&1&&S(0)}function Fn(a,n){if(a&1){let e=$();c(0,"mat-tab-body",13),I("_onCentered",function(){k(e);let i=b();return D(i._removeTabBodyWrapperHeight())})("_onCentering",function(i){k(e);let o=b();return D(o._setTabBodyWrapperHeight(i))})("_beforeCentering",function(i){k(e);let o=b();return D(o._bodyCentered(i))}),l()}if(a&2){let e=n.$implicit,t=n.$index,i=b();Le(e.bodyClass),p("id",i._getTabContentId(t))("content",e.content)("position",e.position)("animationDuration",i.animationDuration)("preserveContent",i.preserveContent),V("tabindex",i.contentTabIndex!=null&&i.selectedIndex===t?i.contentTabIndex:null)("aria-labelledby",i._getTabLabelId(e,t))("aria-hidden",i.selectedIndex!==t)}}var On=new A("MatTabContent"),Ln=(()=>{class a{template=s(G);constructor(){}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","matTabContent",""]],features:[M([{provide:On,useExisting:a}])]})}return a})(),Bn=new A("MatTabLabel"),Fa=new A("MAT_TAB"),Nn=(()=>{class a extends na{_closestTab=s(Fa,{optional:!0});static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275dir=f({type:a,selectors:[["","mat-tab-label",""],["","matTabLabel",""]],features:[M([{provide:Bn,useExisting:a}]),R]})}return a})(),Oa=new A("MAT_TAB_GROUP"),di=(()=>{class a{_viewContainerRef=s(re);_closestTabGroup=s(Oa,{optional:!0});disabled=!1;get templateLabel(){return this._templateLabel}set templateLabel(e){this._setTemplateLabelInput(e)}_templateLabel;_explicitContent=void 0;_implicitContent;textLabel="";ariaLabel;ariaLabelledby;labelClass;bodyClass;id=null;_contentPortal=null;get content(){return this._contentPortal}_stateChanges=new N;position=null;origin=null;isActive=!1;constructor(){s(Ee).load(Pe)}ngOnChanges(e){(e.hasOwnProperty("textLabel")||e.hasOwnProperty("disabled"))&&this._stateChanges.next()}ngOnDestroy(){this._stateChanges.complete()}ngOnInit(){this._contentPortal=new _t(this._explicitContent||this._implicitContent,this._viewContainerRef)}_setTemplateLabelInput(e){e&&e._closestTab===this&&(this._templateLabel=e)}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=x({type:a,selectors:[["mat-tab"]],contentQueries:function(t,i,o){if(t&1&&(E(o,Nn,5),E(o,Ln,7,G)),t&2){let r;_(r=g())&&(i.templateLabel=r.first),_(r=g())&&(i._explicitContent=r.first)}},viewQuery:function(t,i){if(t&1&&O(G,7),t&2){let o;_(o=g())&&(i._implicitContent=o.first)}},hostAttrs:["hidden",""],hostVars:1,hostBindings:function(t,i){t&2&&V("id",null)},inputs:{disabled:[2,"disabled","disabled",y],textLabel:[0,"label","textLabel"],ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],labelClass:"labelClass",bodyClass:"bodyClass",id:"id"},exportAs:["matTab"],features:[M([{provide:Fa,useExisting:a}]),de],ngContentSelectors:li,decls:1,vars:0,template:function(t,i){t&1&&(U(),w(0,vn,1,0,"ng-template"))},encapsulation:2})}return a})(),oi="mdc-tab-indicator--active",Ea="mdc-tab-indicator--no-transition",ri=class{_items;_currentItem;constructor(n){this._items=n}hide(){this._items.forEach(n=>n.deactivateInkBar()),this._currentItem=void 0}alignToElement(n){let e=this._items.find(i=>i.elementRef.nativeElement===n),t=this._currentItem;if(e!==t&&(t?.deactivateInkBar(),e)){let i=t?.elementRef.nativeElement.getBoundingClientRect?.();e.activateInkBar(i),this._currentItem=e}}},Hn=(()=>{class a{_elementRef=s(F);_inkBarElement;_inkBarContentElement;_fitToContent=!1;get fitInkBarToContent(){return this._fitToContent}set fitInkBarToContent(e){this._fitToContent!==e&&(this._fitToContent=e,this._inkBarElement&&this._appendInkBarElement())}activateInkBar(e){let t=this._elementRef.nativeElement;if(!e||!t.getBoundingClientRect||!this._inkBarContentElement){t.classList.add(oi);return}let i=t.getBoundingClientRect(),o=e.width/i.width,r=e.left-i.left;t.classList.add(Ea),this._inkBarContentElement.style.setProperty("transform",`translateX(${r}px) scaleX(${o})`),t.getBoundingClientRect(),t.classList.remove(Ea),t.classList.add(oi),this._inkBarContentElement.style.setProperty("transform","")}deactivateInkBar(){this._elementRef.nativeElement.classList.remove(oi)}ngOnInit(){this._createInkBarElement()}ngOnDestroy(){this._inkBarElement?.remove(),this._inkBarElement=this._inkBarContentElement=null}_createInkBarElement(){let e=this._elementRef.nativeElement.ownerDocument||document,t=this._inkBarElement=e.createElement("span"),i=this._inkBarContentElement=e.createElement("span");t.className="mdc-tab-indicator",i.className="mdc-tab-indicator__content mdc-tab-indicator__content--underline",t.appendChild(this._inkBarContentElement),this._appendInkBarElement()}_appendInkBarElement(){this._inkBarElement;let e=this._fitToContent?this._elementRef.nativeElement.querySelector(".mdc-tab__content"):this._elementRef.nativeElement;e.appendChild(this._inkBarElement)}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,inputs:{fitInkBarToContent:[2,"fitInkBarToContent","fitInkBarToContent",y]}})}return a})();var La=(()=>{class a extends Hn{elementRef=s(F);disabled=!1;focus(){this.elementRef.nativeElement.focus()}getOffsetLeft(){return this.elementRef.nativeElement.offsetLeft}getOffsetWidth(){return this.elementRef.nativeElement.offsetWidth}static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275dir=f({type:a,selectors:[["","matTabLabelWrapper",""]],hostVars:3,hostBindings:function(t,i){t&2&&(V("aria-disabled",!!i.disabled),T("mat-mdc-tab-disabled",i.disabled))},inputs:{disabled:[2,"disabled","disabled",y]},features:[R]})}return a})(),Pa={passive:!0},Vn=650,zn=100,jn=(()=>{class a{_elementRef=s(F);_changeDetectorRef=s(Y);_viewportRuler=s(bt);_dir=s(ye,{optional:!0});_ngZone=s(oe);_platform=s(Ie);_sharedResizeObserver=s(Ca);_injector=s(ge);_renderer=s(et);_animationMode=s(he,{optional:!0});_eventCleanups;_scrollDistance=0;_selectedIndexChanged=!1;_destroyed=new N;_showPaginationControls=!1;_disableScrollAfter=!0;_disableScrollBefore=!0;_tabLabelCount;_scrollDistanceChanged;_keyManager;_currentTextContent;_stopScrolling=new N;disablePagination=!1;get selectedIndex(){return this._selectedIndex}set selectedIndex(e){let t=isNaN(e)?0:e;this._selectedIndex!=t&&(this._selectedIndexChanged=!0,this._selectedIndex=t,this._keyManager&&this._keyManager.updateActiveItem(t))}_selectedIndex=0;selectFocusedIndex=new C;indexFocused=new C;constructor(){this._eventCleanups=this._ngZone.runOutsideAngular(()=>[this._renderer.listen(this._elementRef.nativeElement,"mouseleave",()=>this._stopInterval())])}ngAfterViewInit(){this._eventCleanups.push(Jt(this._renderer,this._previousPaginator.nativeElement,"touchstart",()=>this._handlePaginatorPress("before"),Pa),Jt(this._renderer,this._nextPaginator.nativeElement,"touchstart",()=>this._handlePaginatorPress("after"),Pa))}ngAfterContentInit(){let e=this._dir?this._dir.change:xe("ltr"),t=this._sharedResizeObserver.observe(this._elementRef.nativeElement).pipe(Hi(32),P(this._destroyed)),i=this._viewportRuler.change(150).pipe(P(this._destroyed)),o=()=>{this.updatePagination(),this._alignInkBarToSelectedTab()};this._keyManager=new je(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap().skipPredicate(()=>!1),this._keyManager.updateActiveItem(this._selectedIndex),pe(o,{injector:this._injector}),ae(e,i,t,this._items.changes,this._itemsResized()).pipe(P(this._destroyed)).subscribe(()=>{this._ngZone.run(()=>{Promise.resolve().then(()=>{this._scrollDistance=Math.max(0,Math.min(this._getMaxScrollDistance(),this._scrollDistance)),o()})}),this._keyManager.withHorizontalOrientation(this._getLayoutDirection())}),this._keyManager.change.subscribe(r=>{this.indexFocused.emit(r),this._setTabFocus(r)})}_itemsResized(){return typeof ResizeObserver!="function"?ot:this._items.changes.pipe(ne(this._items),ct(e=>new Li(t=>this._ngZone.runOutsideAngular(()=>{let i=new ResizeObserver(o=>t.next(o));return e.forEach(o=>i.observe(o.elementRef.nativeElement)),()=>{i.disconnect()}}))),zi(1),Te(e=>e.some(t=>t.contentRect.width>0&&t.contentRect.height>0)))}ngAfterContentChecked(){this._tabLabelCount!=this._items.length&&(this.updatePagination(),this._tabLabelCount=this._items.length,this._changeDetectorRef.markForCheck()),this._selectedIndexChanged&&(this._scrollToLabel(this._selectedIndex),this._checkScrollingControls(),this._alignInkBarToSelectedTab(),this._selectedIndexChanged=!1,this._changeDetectorRef.markForCheck()),this._scrollDistanceChanged&&(this._updateTabScrollPosition(),this._scrollDistanceChanged=!1,this._changeDetectorRef.markForCheck())}ngOnDestroy(){this._eventCleanups.forEach(e=>e()),this._keyManager?.destroy(),this._destroyed.next(),this._destroyed.complete(),this._stopScrolling.complete()}_handleKeydown(e){if(!ze(e))switch(e.keyCode){case 13:case 32:if(this.focusIndex!==this.selectedIndex){let t=this._items.get(this.focusIndex);t&&!t.disabled&&(this.selectFocusedIndex.emit(this.focusIndex),this._itemSelected(e))}break;default:this._keyManager.onKeydown(e)}}_onContentChanges(){let e=this._elementRef.nativeElement.textContent;e!==this._currentTextContent&&(this._currentTextContent=e||"",this._ngZone.run(()=>{this.updatePagination(),this._alignInkBarToSelectedTab(),this._changeDetectorRef.markForCheck()}))}updatePagination(){this._checkPaginationEnabled(),this._checkScrollingControls(),this._updateTabScrollPosition()}get focusIndex(){return this._keyManager?this._keyManager.activeItemIndex:0}set focusIndex(e){!this._isValidIndex(e)||this.focusIndex===e||!this._keyManager||this._keyManager.setActiveItem(e)}_isValidIndex(e){return this._items?!!this._items.toArray()[e]:!0}_setTabFocus(e){if(this._showPaginationControls&&this._scrollToLabel(e),this._items&&this._items.length){this._items.toArray()[e].focus();let t=this._tabListContainer.nativeElement;this._getLayoutDirection()=="ltr"?t.scrollLeft=0:t.scrollLeft=t.scrollWidth-t.offsetWidth}}_getLayoutDirection(){return this._dir&&this._dir.value==="rtl"?"rtl":"ltr"}_updateTabScrollPosition(){if(this.disablePagination)return;let e=this.scrollDistance,t=this._getLayoutDirection()==="ltr"?-e:e;this._tabList.nativeElement.style.transform=`translateX(${Math.round(t)}px)`,(this._platform.TRIDENT||this._platform.EDGE)&&(this._tabListContainer.nativeElement.scrollLeft=0)}get scrollDistance(){return this._scrollDistance}set scrollDistance(e){this._scrollTo(e)}_scrollHeader(e){let t=this._tabListContainer.nativeElement.offsetWidth,i=(e=="before"?-1:1)*t/3;return this._scrollTo(this._scrollDistance+i)}_handlePaginatorClick(e){this._stopInterval(),this._scrollHeader(e)}_scrollToLabel(e){if(this.disablePagination)return;let t=this._items?this._items.toArray()[e]:null;if(!t)return;let i=this._tabListContainer.nativeElement.offsetWidth,{offsetLeft:o,offsetWidth:r}=t.elementRef.nativeElement,m,u;this._getLayoutDirection()=="ltr"?(m=o,u=m+r):(u=this._tabListInner.nativeElement.offsetWidth-o,m=u-r);let L=this.scrollDistance,j=this.scrollDistance+i;m<L?this.scrollDistance-=L-m:u>j&&(this.scrollDistance+=Math.min(u-j,m-L))}_checkPaginationEnabled(){if(this.disablePagination)this._showPaginationControls=!1;else{let e=this._tabListInner.nativeElement.scrollWidth,t=this._elementRef.nativeElement.offsetWidth,i=e-t>=5;i||(this.scrollDistance=0),i!==this._showPaginationControls&&(this._showPaginationControls=i,this._changeDetectorRef.markForCheck())}}_checkScrollingControls(){this.disablePagination?this._disableScrollAfter=this._disableScrollBefore=!0:(this._disableScrollBefore=this.scrollDistance==0,this._disableScrollAfter=this.scrollDistance==this._getMaxScrollDistance(),this._changeDetectorRef.markForCheck())}_getMaxScrollDistance(){let e=this._tabListInner.nativeElement.scrollWidth,t=this._tabListContainer.nativeElement.offsetWidth;return e-t||0}_alignInkBarToSelectedTab(){let e=this._items&&this._items.length?this._items.toArray()[this.selectedIndex]:null,t=e?e.elementRef.nativeElement:null;t?this._inkBar.alignToElement(t):this._inkBar.hide()}_stopInterval(){this._stopScrolling.next()}_handlePaginatorPress(e,t){t&&t.button!=null&&t.button!==0||(this._stopInterval(),Ni(Vn,zn).pipe(P(ae(this._stopScrolling,this._destroyed))).subscribe(()=>{let{maxScrollDistance:i,distance:o}=this._scrollHeader(e);(o===0||o>=i)&&this._stopInterval()}))}_scrollTo(e){if(this.disablePagination)return{maxScrollDistance:0,distance:0};let t=this._getMaxScrollDistance();return this._scrollDistance=Math.max(0,Math.min(t,e)),this._scrollDistanceChanged=!0,this._checkScrollingControls(),{maxScrollDistance:t,distance:this._scrollDistance}}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,inputs:{disablePagination:[2,"disablePagination","disablePagination",y],selectedIndex:[2,"selectedIndex","selectedIndex",be]},outputs:{selectFocusedIndex:"selectFocusedIndex",indexFocused:"indexFocused"}})}return a})(),Qn=(()=>{class a extends jn{_items;_tabListContainer;_tabList;_tabListInner;_nextPaginator;_previousPaginator;_inkBar;ariaLabel;ariaLabelledby;disableRipple=!1;ngAfterContentInit(){this._inkBar=new ri(this._items),super.ngAfterContentInit()}_itemSelected(e){e.preventDefault()}static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275cmp=x({type:a,selectors:[["mat-tab-header"]],contentQueries:function(t,i,o){if(t&1&&E(o,La,4),t&2){let r;_(r=g())&&(i._items=r)}},viewQuery:function(t,i){if(t&1&&(O(yn,7),O(wn,7),O(Cn,7),O(xn,5),O(kn,5)),t&2){let o;_(o=g())&&(i._tabListContainer=o.first),_(o=g())&&(i._tabList=o.first),_(o=g())&&(i._tabListInner=o.first),_(o=g())&&(i._nextPaginator=o.first),_(o=g())&&(i._previousPaginator=o.first)}},hostAttrs:[1,"mat-mdc-tab-header"],hostVars:4,hostBindings:function(t,i){t&2&&T("mat-mdc-tab-header-pagination-controls-enabled",i._showPaginationControls)("mat-mdc-tab-header-rtl",i._getLayoutDirection()=="rtl")},inputs:{ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],disableRipple:[2,"disableRipple","disableRipple",y]},features:[R],ngContentSelectors:li,decls:13,vars:10,consts:[["previousPaginator",""],["tabListContainer",""],["tabList",""],["tabListInner",""],["nextPaginator",""],["mat-ripple","",1,"mat-mdc-tab-header-pagination","mat-mdc-tab-header-pagination-before",3,"click","mousedown","touchend","matRippleDisabled"],[1,"mat-mdc-tab-header-pagination-chevron"],[1,"mat-mdc-tab-label-container",3,"keydown"],["role","tablist",1,"mat-mdc-tab-list",3,"cdkObserveContent"],[1,"mat-mdc-tab-labels"],["mat-ripple","",1,"mat-mdc-tab-header-pagination","mat-mdc-tab-header-pagination-after",3,"mousedown","click","touchend","matRippleDisabled"]],template:function(t,i){if(t&1){let o=$();U(),c(0,"div",5,0),I("click",function(){return k(o),D(i._handlePaginatorClick("before"))})("mousedown",function(m){return k(o),D(i._handlePaginatorPress("before",m))})("touchend",function(){return k(o),D(i._stopInterval())}),B(2,"div",6),l(),c(3,"div",7,1),I("keydown",function(m){return k(o),D(i._handleKeydown(m))}),c(5,"div",8,2),I("cdkObserveContent",function(){return k(o),D(i._onContentChanges())}),c(7,"div",9,3),S(9),l()()(),c(10,"div",10,4),I("mousedown",function(m){return k(o),D(i._handlePaginatorPress("after",m))})("click",function(){return k(o),D(i._handlePaginatorClick("after"))})("touchend",function(){return k(o),D(i._stopInterval())}),B(12,"div",6),l()}t&2&&(T("mat-mdc-tab-header-pagination-disabled",i._disableScrollBefore),p("matRippleDisabled",i._disableScrollBefore||i.disableRipple),d(3),T("_mat-animation-noopable",i._animationMode==="NoopAnimations"),d(2),V("aria-label",i.ariaLabel||null)("aria-labelledby",i.ariaLabelledby||null),d(5),T("mat-mdc-tab-header-pagination-disabled",i._disableScrollAfter),p("matRippleDisabled",i._disableScrollAfter||i.disableRipple))},dependencies:[ai,Zi],styles:[`.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height, 1px);border-bottom-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height, 1px);border-top-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mdc-secondary-navigation-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}
`],encapsulation:2})}return a})(),Un=new A("MAT_TABS_CONFIG"),Aa=(()=>{class a extends it{_host=s(si);_centeringSub=le.EMPTY;_leavingSub=le.EMPTY;constructor(){super()}ngOnInit(){super.ngOnInit(),this._centeringSub=this._host._beforeCentering.pipe(ne(this._host._isCenterPosition())).subscribe(e=>{this._host._content&&e&&!this.hasAttached()&&this.attach(this._host._content)}),this._leavingSub=this._host._afterLeavingCenter.subscribe(()=>{this._host.preserveContent||this.detach()})}ngOnDestroy(){super.ngOnDestroy(),this._centeringSub.unsubscribe(),this._leavingSub.unsubscribe()}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","matTabBodyHost",""]],features:[R]})}return a})(),si=(()=>{class a{_elementRef=s(F);_dir=s(ye,{optional:!0});_ngZone=s(oe);_injector=s(ge);_renderer=s(et);_animationsModule=s(he,{optional:!0});_eventCleanups;_initialized;_fallbackTimer;_positionIndex;_dirChangeSubscription=le.EMPTY;_position;_previousPosition;_onCentering=new C;_beforeCentering=new C;_afterLeavingCenter=new C;_onCentered=new C(!0);_portalHost;_contentElement;_content;animationDuration="500ms";preserveContent=!1;set position(e){this._positionIndex=e,this._computePositionAnimationState()}constructor(){if(this._dir){let e=s(Y);this._dirChangeSubscription=this._dir.change.subscribe(t=>{this._computePositionAnimationState(t),e.markForCheck()})}}ngOnInit(){this._bindTransitionEvents(),this._position==="center"&&(this._setActiveClass(!0),pe(()=>this._onCentering.emit(this._elementRef.nativeElement.clientHeight),{injector:this._injector})),this._initialized=!0}ngOnDestroy(){clearTimeout(this._fallbackTimer),this._eventCleanups?.forEach(e=>e()),this._dirChangeSubscription.unsubscribe()}_bindTransitionEvents(){this._ngZone.runOutsideAngular(()=>{let e=this._elementRef.nativeElement,t=i=>{i.target===this._contentElement?.nativeElement&&(this._elementRef.nativeElement.classList.remove("mat-tab-body-animating"),i.type==="transitionend"&&this._transitionDone())};this._eventCleanups=[this._renderer.listen(e,"transitionstart",i=>{i.target===this._contentElement?.nativeElement&&(this._elementRef.nativeElement.classList.add("mat-tab-body-animating"),this._transitionStarted())}),this._renderer.listen(e,"transitionend",t),this._renderer.listen(e,"transitioncancel",t)]})}_transitionStarted(){clearTimeout(this._fallbackTimer);let e=this._position==="center";this._beforeCentering.emit(e),e&&this._onCentering.emit(this._elementRef.nativeElement.clientHeight)}_transitionDone(){this._position==="center"?this._onCentered.emit():this._previousPosition==="center"&&this._afterLeavingCenter.emit()}_setActiveClass(e){this._elementRef.nativeElement.classList.toggle("mat-mdc-tab-body-active",e)}_getLayoutDirection(){return this._dir&&this._dir.value==="rtl"?"rtl":"ltr"}_isCenterPosition(){return this._positionIndex===0}_computePositionAnimationState(e=this._getLayoutDirection()){this._previousPosition=this._position,this._positionIndex<0?this._position=e=="ltr"?"left":"right":this._positionIndex>0?this._position=e=="ltr"?"right":"left":this._position="center",this._animationsDisabled()?this._simulateTransitionEvents():this._initialized&&(this._position==="center"||this._previousPosition==="center")&&(clearTimeout(this._fallbackTimer),this._fallbackTimer=this._ngZone.runOutsideAngular(()=>setTimeout(()=>this._simulateTransitionEvents(),100)))}_simulateTransitionEvents(){this._transitionStarted(),pe(()=>this._transitionDone(),{injector:this._injector})}_animationsDisabled(){return this._animationsModule==="NoopAnimations"||this.animationDuration==="0ms"||this.animationDuration==="0s"}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=x({type:a,selectors:[["mat-tab-body"]],viewQuery:function(t,i){if(t&1&&(O(Aa,5),O(Dn,5)),t&2){let o;_(o=g())&&(i._portalHost=o.first),_(o=g())&&(i._contentElement=o.first)}},hostAttrs:[1,"mat-mdc-tab-body"],hostVars:1,hostBindings:function(t,i){t&2&&V("inert",i._position==="center"?null:"")},inputs:{_content:[0,"content","_content"],animationDuration:"animationDuration",preserveContent:"preserveContent",position:"position"},outputs:{_onCentering:"_onCentering",_beforeCentering:"_beforeCentering",_onCentered:"_onCentered"},decls:3,vars:6,consts:[["content",""],["cdkScrollable","",1,"mat-mdc-tab-body-content"],["matTabBodyHost",""]],template:function(t,i){t&1&&(c(0,"div",1,0),w(2,Rn,0,0,"ng-template",2),l()),t&2&&T("mat-tab-body-content-left",i._position==="left")("mat-tab-body-content-right",i._position==="right")("mat-tab-body-content-can-animate",i._position==="center"||i._previousPosition==="center")},dependencies:[Aa,la],styles:[`.mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}
`],encapsulation:2})}return a})(),Ba=(()=>{class a{_elementRef=s(F);_changeDetectorRef=s(Y);_ngZone=s(oe);_tabsSubscription=le.EMPTY;_tabLabelSubscription=le.EMPTY;_tabBodySubscription=le.EMPTY;_diAnimationsDisabled=s(he,{optional:!0})==="NoopAnimations";_allTabs;_tabBodies;_tabBodyWrapper;_tabHeader;_tabs=new Fe;_indexToSelect=0;_lastFocusedTabIndex=null;_tabBodyWrapperHeight=0;color;get fitInkBarToContent(){return this._fitInkBarToContent}set fitInkBarToContent(e){this._fitInkBarToContent=e,this._changeDetectorRef.markForCheck()}_fitInkBarToContent=!1;stretchTabs=!0;alignTabs=null;dynamicHeight=!1;get selectedIndex(){return this._selectedIndex}set selectedIndex(e){this._indexToSelect=isNaN(e)?null:e}_selectedIndex=null;headerPosition="above";get animationDuration(){return this._animationDuration}set animationDuration(e){let t=e+"";this._animationDuration=/^\d+$/.test(t)?e+"ms":t}_animationDuration;get contentTabIndex(){return this._contentTabIndex}set contentTabIndex(e){this._contentTabIndex=isNaN(e)?null:e}_contentTabIndex;disablePagination=!1;disableRipple=!1;preserveContent=!1;get backgroundColor(){return this._backgroundColor}set backgroundColor(e){let t=this._elementRef.nativeElement.classList;t.remove("mat-tabs-with-background",`mat-background-${this.backgroundColor}`),e&&t.add("mat-tabs-with-background",`mat-background-${e}`),this._backgroundColor=e}_backgroundColor;ariaLabel;ariaLabelledby;selectedIndexChange=new C;focusChange=new C;animationDone=new C;selectedTabChange=new C(!0);_groupId;_isServer=!s(Ie).isBrowser;constructor(){let e=s(Un,{optional:!0});this._groupId=s(ve).getId("mat-tab-group-"),this.animationDuration=e&&e.animationDuration?e.animationDuration:"500ms",this.disablePagination=e&&e.disablePagination!=null?e.disablePagination:!1,this.dynamicHeight=e&&e.dynamicHeight!=null?e.dynamicHeight:!1,e?.contentTabIndex!=null&&(this.contentTabIndex=e.contentTabIndex),this.preserveContent=!!e?.preserveContent,this.fitInkBarToContent=e&&e.fitInkBarToContent!=null?e.fitInkBarToContent:!1,this.stretchTabs=e&&e.stretchTabs!=null?e.stretchTabs:!0,this.alignTabs=e&&e.alignTabs!=null?e.alignTabs:null}ngAfterContentChecked(){let e=this._indexToSelect=this._clampTabIndex(this._indexToSelect);if(this._selectedIndex!=e){let t=this._selectedIndex==null;if(!t){this.selectedTabChange.emit(this._createChangeEvent(e));let i=this._tabBodyWrapper.nativeElement;i.style.minHeight=i.clientHeight+"px"}Promise.resolve().then(()=>{this._tabs.forEach((i,o)=>i.isActive=o===e),t||(this.selectedIndexChange.emit(e),this._tabBodyWrapper.nativeElement.style.minHeight="")})}this._tabs.forEach((t,i)=>{t.position=i-e,this._selectedIndex!=null&&t.position==0&&!t.origin&&(t.origin=e-this._selectedIndex)}),this._selectedIndex!==e&&(this._selectedIndex=e,this._lastFocusedTabIndex=null,this._changeDetectorRef.markForCheck())}ngAfterContentInit(){this._subscribeToAllTabChanges(),this._subscribeToTabLabels(),this._tabsSubscription=this._tabs.changes.subscribe(()=>{let e=this._clampTabIndex(this._indexToSelect);if(e===this._selectedIndex){let t=this._tabs.toArray(),i;for(let o=0;o<t.length;o++)if(t[o].isActive){this._indexToSelect=this._selectedIndex=o,this._lastFocusedTabIndex=null,i=t[o];break}!i&&t[e]&&Promise.resolve().then(()=>{t[e].isActive=!0,this.selectedTabChange.emit(this._createChangeEvent(e))})}this._changeDetectorRef.markForCheck()})}ngAfterViewInit(){this._tabBodySubscription=this._tabBodies.changes.subscribe(()=>this._bodyCentered(!0))}_subscribeToAllTabChanges(){this._allTabs.changes.pipe(ne(this._allTabs)).subscribe(e=>{this._tabs.reset(e.filter(t=>t._closestTabGroup===this||!t._closestTabGroup)),this._tabs.notifyOnChanges()})}ngOnDestroy(){this._tabs.destroy(),this._tabsSubscription.unsubscribe(),this._tabLabelSubscription.unsubscribe(),this._tabBodySubscription.unsubscribe()}realignInkBar(){this._tabHeader&&this._tabHeader._alignInkBarToSelectedTab()}updatePagination(){this._tabHeader&&this._tabHeader.updatePagination()}focusTab(e){let t=this._tabHeader;t&&(t.focusIndex=e)}_focusChanged(e){this._lastFocusedTabIndex=e,this.focusChange.emit(this._createChangeEvent(e))}_createChangeEvent(e){let t=new ci;return t.index=e,this._tabs&&this._tabs.length&&(t.tab=this._tabs.toArray()[e]),t}_subscribeToTabLabels(){this._tabLabelSubscription&&this._tabLabelSubscription.unsubscribe(),this._tabLabelSubscription=ae(...this._tabs.map(e=>e._stateChanges)).subscribe(()=>this._changeDetectorRef.markForCheck())}_clampTabIndex(e){return Math.min(this._tabs.length-1,Math.max(e||0,0))}_getTabLabelId(e,t){return e.id||`${this._groupId}-label-${t}`}_getTabContentId(e){return`${this._groupId}-content-${e}`}_setTabBodyWrapperHeight(e){if(!this.dynamicHeight||!this._tabBodyWrapperHeight){this._tabBodyWrapperHeight=e;return}let t=this._tabBodyWrapper.nativeElement;t.style.height=this._tabBodyWrapperHeight+"px",this._tabBodyWrapper.nativeElement.offsetHeight&&(t.style.height=e+"px")}_removeTabBodyWrapperHeight(){let e=this._tabBodyWrapper.nativeElement;this._tabBodyWrapperHeight=e.clientHeight,e.style.height="",this._ngZone.run(()=>this.animationDone.emit())}_handleClick(e,t,i){t.focusIndex=i,e.disabled||(this.selectedIndex=i)}_getTabIndex(e){let t=this._lastFocusedTabIndex??this.selectedIndex;return e===t?0:-1}_tabFocusChanged(e,t){e&&e!=="mouse"&&e!=="touch"&&(this._tabHeader.focusIndex=t)}_bodyCentered(e){e&&this._tabBodies?.forEach((t,i)=>t._setActiveClass(i===this._selectedIndex))}_animationsDisabled(){return this._diAnimationsDisabled||this.animationDuration==="0"||this.animationDuration==="0ms"}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=x({type:a,selectors:[["mat-tab-group"]],contentQueries:function(t,i,o){if(t&1&&E(o,di,5),t&2){let r;_(r=g())&&(i._allTabs=r)}},viewQuery:function(t,i){if(t&1&&(O(Sn,5),O(Tn,5),O(si,5)),t&2){let o;_(o=g())&&(i._tabBodyWrapper=o.first),_(o=g())&&(i._tabHeader=o.first),_(o=g())&&(i._tabBodies=o)}},hostAttrs:[1,"mat-mdc-tab-group"],hostVars:11,hostBindings:function(t,i){t&2&&(V("mat-align-tabs",i.alignTabs),Le("mat-"+(i.color||"primary")),Oe("--mat-tab-animation-duration",i.animationDuration),T("mat-mdc-tab-group-dynamic-height",i.dynamicHeight)("mat-mdc-tab-group-inverted-header",i.headerPosition==="below")("mat-mdc-tab-group-stretch-tabs",i.stretchTabs))},inputs:{color:"color",fitInkBarToContent:[2,"fitInkBarToContent","fitInkBarToContent",y],stretchTabs:[2,"mat-stretch-tabs","stretchTabs",y],alignTabs:[0,"mat-align-tabs","alignTabs"],dynamicHeight:[2,"dynamicHeight","dynamicHeight",y],selectedIndex:[2,"selectedIndex","selectedIndex",be],headerPosition:"headerPosition",animationDuration:"animationDuration",contentTabIndex:[2,"contentTabIndex","contentTabIndex",be],disablePagination:[2,"disablePagination","disablePagination",y],disableRipple:[2,"disableRipple","disableRipple",y],preserveContent:[2,"preserveContent","preserveContent",y],backgroundColor:"backgroundColor",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"]},outputs:{selectedIndexChange:"selectedIndexChange",focusChange:"focusChange",animationDone:"animationDone",selectedTabChange:"selectedTabChange"},exportAs:["matTabGroup"],features:[M([{provide:Oa,useExisting:a}])],ngContentSelectors:li,decls:9,vars:8,consts:[["tabHeader",""],["tabBodyWrapper",""],["tabNode",""],[3,"indexFocused","selectFocusedIndex","selectedIndex","disableRipple","disablePagination","aria-label","aria-labelledby"],["role","tab","matTabLabelWrapper","","cdkMonitorElementFocus","",1,"mdc-tab","mat-mdc-tab","mat-focus-indicator",3,"id","mdc-tab--active","class","disabled","fitInkBarToContent"],[1,"mat-mdc-tab-body-wrapper"],["role","tabpanel",3,"id","class","content","position","animationDuration","preserveContent"],["role","tab","matTabLabelWrapper","","cdkMonitorElementFocus","",1,"mdc-tab","mat-mdc-tab","mat-focus-indicator",3,"click","cdkFocusChange","id","disabled","fitInkBarToContent"],[1,"mdc-tab__ripple"],["mat-ripple","",1,"mat-mdc-tab-ripple",3,"matRippleTrigger","matRippleDisabled"],[1,"mdc-tab__content"],[1,"mdc-tab__text-label"],[3,"cdkPortalOutlet"],["role","tabpanel",3,"_onCentered","_onCentering","_beforeCentering","id","content","position","animationDuration","preserveContent"]],template:function(t,i){if(t&1){let o=$();U(),c(0,"mat-tab-header",3,0),I("indexFocused",function(m){return k(o),D(i._focusChanged(m))})("selectFocusedIndex",function(m){return k(o),D(i.selectedIndex=m)}),Kt(2,Pn,8,17,"div",4,Yt),l(),w(4,An,1,0),c(5,"div",5,1),Kt(7,Fn,1,10,"mat-tab-body",6,Yt),l()}t&2&&(p("selectedIndex",i.selectedIndex||0)("disableRipple",i.disableRipple)("disablePagination",i.disablePagination)("aria-label",i.ariaLabel)("aria-labelledby",i.ariaLabelledby),d(2),Xt(i._tabs),d(2),Q(i._isServer?4:-1),d(),T("_mat-animation-noopable",i._animationsDisabled()),d(2),Xt(i._tabs))},dependencies:[Qn,La,Ki,ai,it,si],styles:[`.mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mdc-secondary-navigation-tab-container-height, 48px);font-family:var(--mat-tab-header-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-header-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-header-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-header-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-header-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mdc-tab-indicator-active-indicator-height, 2px);border-radius:var(--mdc-tab-indicator-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:"";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}
`],encapsulation:2})}return a})(),ci=class{index;tab};var Na=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=X({type:a});static \u0275inj=K({imports:[ee,ee]})}return a})();var Re=class a{constructor(n){this.authService=n;this.supabase=n.supabase}supabase;ratingsSubject=new ie([]);ratings$=this.ratingsSubject.asObservable();submitRating(n,e,t,i,o){return v(this,null,function*(){try{let{data:r,error:m}=yield this.supabase.from("ratings").insert([{ride_id:n,rater_id:e,rated_id:t,rating:i,feedback:o}]).select().single();if(m)throw m;let u=this.ratingsSubject.value;return this.ratingsSubject.next([...u,r]),r}catch(r){throw console.error("Error submitting rating:",r),r}})}hasUserRated(n,e,t){return v(this,null,function*(){try{let{data:i,error:o}=yield this.supabase.from("ratings").select("id").eq("ride_id",n).eq("rater_id",e).eq("rated_id",t);if(o)throw o;return i.length>0}catch(i){return console.error("Error checking if user has rated:",i),!1}})}getRatingsByUser(n){return v(this,null,function*(){try{let{data:e,error:t}=yield this.supabase.from("ratings").select("*").eq("rater_id",n).order("created_at",{ascending:!1});if(t)throw t;return e}catch(e){return console.error("Error fetching ratings by user:",e),[]}})}getRatingsForUser(n){return v(this,null,function*(){try{let{data:e,error:t}=yield this.supabase.from("ratings").select("*").eq("rater_id",n).order("created_at",{ascending:!1});if(t)throw t;return e}catch(e){return console.error("Error fetching ratings for user:",e),[]}})}getUserRatingSummary(n){return v(this,null,function*(){try{let e=yield this.getRatingsForUser(n),t=e.length,i=t>0?e.reduce((r,m)=>r+m.rating,0)/t:0,o=e.slice(0,5);return{averageRating:i,totalRatings:t,recentRatings:o}}catch(e){return console.error("Error getting user rating summary:",e),{averageRating:0,totalRatings:0,recentRatings:[]}}})}getRatingsForRide(n){return v(this,null,function*(){try{let{data:e,error:t}=yield this.supabase.from("ratings").select("*").eq("ride_id",n);if(t)throw t;return e}catch(e){return console.error("Error fetching ratings for ride:",e),[]}})}static \u0275fac=function(e){return new(e||a)(_e(ue))};static \u0275prov=ke({token:a,factory:a.\u0275fac,providedIn:"root"})};function Xn(a,n){if(a&1){let e=$();c(0,"button",9),I("click",function(){let i=k(e).index,o=b(2);return D(o.selectRating(i+1))}),c(1,"mat-icon"),h(2),l()()}if(a&2){let e=n.index,t=b(2);p("color",e<t.selectedRating?"accent":""),d(2),z(e<t.selectedRating?"star":"star_border")}}function Zn(a,n){if(a&1){let e=$();c(0,"mat-card")(1,"mat-card-header")(2,"mat-card-title"),h(3),l(),c(4,"mat-card-subtitle"),h(5),l()(),c(6,"mat-card-content")(7,"form",1),I("ngSubmit",function(){k(e);let i=b();return D(i.onSubmit())}),c(8,"div",2),w(9,Xn,3,2,"button",3),l(),c(10,"mat-form-field",4)(11,"mat-label"),h(12,"Feedback (optional)"),l(),B(13,"textarea",5),l(),c(14,"div",6)(15,"button",7),I("click",function(){k(e);let i=b();return D(i.onCancel())}),h(16,"Cancel"),l(),c(17,"button",8),h(18," Submit Rating "),l()()()()()}if(a&2){let e=b();d(3),J("Rate ",e.userToRate.full_name||e.userToRate.email,""),d(2),mt(" How was your ride from ",e.ride.pickup_location," to ",e.ride.dropoff_location,"? "),d(2),p("formGroup",e.ratingForm),d(2),p("ngForOf",e.stars),d(8),p("disabled",e.ratingForm.invalid||e.submitting)}}var Ht=class a{constructor(n,e,t,i){this.fb=n;this.ratingService=e;this.authService=t;this.snackBar=i}ride;userToRate;ratingSubmitted=new C;ratingCancelled=new C;ratingForm;stars=[1,2,3,4,5];selectedRating=0;submitting=!1;currentUser=null;ngOnInit(){this.initForm(),this.loadCurrentUser()}loadCurrentUser(){return v(this,null,function*(){this.currentUser=yield this.authService.getCurrentUser()})}initForm(){this.ratingForm=this.fb.group({rating:[0,[we.required,we.min(1),we.max(5)]],feedback:[""]})}selectRating(n){this.selectedRating=n,this.ratingForm.patchValue({rating:n})}onSubmit(){return v(this,null,function*(){if(!(this.ratingForm.invalid||!this.currentUser)){this.submitting=!0;try{if(yield this.ratingService.hasUserRated(this.ride.id,this.currentUser.id,this.userToRate.id)){this.snackBar.open("You have already rated this ride","Close",{duration:3e3}),this.ratingSubmitted.emit(!1);return}yield this.ratingService.submitRating(this.ride.id,this.currentUser.id,this.userToRate.id,this.ratingForm.value.rating,this.ratingForm.value.feedback),this.snackBar.open("Rating submitted successfully","Close",{duration:3e3}),this.ratingSubmitted.emit(!0)}catch(n){console.error("Error submitting rating:",n),this.snackBar.open("Failed to submit rating","Close",{duration:3e3}),this.ratingSubmitted.emit(!1)}finally{this.submitting=!1}}})}onCancel(){this.ratingCancelled.emit()}static \u0275fac=function(e){return new(e||a)(W(wa),W(Re),W(ue),W(yt))};static \u0275cmp=x({type:a,selectors:[["app-rating-form"]],inputs:{ride:"ride",userToRate:"userToRate"},outputs:{ratingSubmitted:"ratingSubmitted",ratingCancelled:"ratingCancelled"},decls:1,vars:1,consts:[[4,"ngIf"],[3,"ngSubmit","formGroup"],[1,"star-rating"],["type","button","mat-icon-button","",3,"color","click",4,"ngFor","ngForOf"],["appearance","outline",1,"feedback-field"],["matInput","","formControlName","feedback","rows","4","placeholder","Share your experience..."],[1,"form-actions"],["mat-button","","type","button",3,"click"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],["type","button","mat-icon-button","",3,"click","color"]],template:function(e,t){e&1&&w(0,Zn,19,6,"mat-card",0),e&2&&p("ngIf",t.ride&&t.userToRate)},dependencies:[Ve,ht,He,kt,_a,Ct,xt,ua,ni,va,ft,ut,pt,Ke,qe,We,Ye,$e,Ge,It,Rt,Dt,Et,Mt,Ue,Qe,wt],styles:[".star-rating[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:20px 0}.feedback-field[_ngcontent-%COMP%]{width:100%}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-top:20px}.form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-left:10px}"]})};function to(a,n){if(a&1&&(c(0,"mat-card-subtitle"),h(1),Zt(2,"number"),l()),a&2){let e=b(2);d(),mt(" ",Ui(2,2,e.ratingSummary.averageRating,"1.1-1")," stars from ",e.ratingSummary.totalRatings," ratings ")}}function io(a,n){a&1&&(c(0,"mat-card-subtitle"),h(1," No ratings yet "),l())}function ao(a,n){if(a&1&&(c(0,"mat-icon",5),h(1),l()),a&2){let e=n.$implicit;p("ngClass",e),d(),J(" ",e==="full"?"star":e==="half"?"star_half":"star_border"," ")}}function no(a,n){a&1&&B(0,"mat-divider",6)}function oo(a,n){a&1&&(c(0,"mat-icon",17),h(1," star "),l())}function ro(a,n){if(a&1&&(c(0,"p",18),h(1),l()),a&2){let e=b().$implicit;d(),z(e.feedback)}}function so(a,n){if(a&1&&(c(0,"div",9)(1,"div",10)(2,"div",11),w(3,oo,2,0,"mat-icon",12),l(),c(4,"div",13)(5,"span",14),h(6),l(),c(7,"span",15),h(8),Zt(9,"date"),l()()(),w(10,ro,2,1,"p",16),l()),a&2){let e=n.$implicit,t=b(3);d(3),p("ngForOf",t.getFullStars(e.rating)),d(3),z(t.getRaterName(e.rater_id)),d(2),z(Qi(9,4,e.created_at)),d(2),p("ngIf",e.feedback)}}function co(a,n){if(a&1&&(c(0,"div",7)(1,"h3"),h(2,"Recent Feedback"),l(),w(3,so,11,6,"div",8),l()),a&2){let e=b(2);d(3),p("ngForOf",e.ratingSummary.recentRatings)}}function lo(a,n){if(a&1&&(c(0,"mat-card")(1,"mat-card-header")(2,"mat-card-title"),h(3,"Ratings & Feedback"),l(),w(4,to,3,5,"mat-card-subtitle",0)(5,io,2,0,"mat-card-subtitle",0),l(),c(6,"mat-card-content")(7,"div",1),w(8,ao,2,2,"mat-icon",2),l(),w(9,no,1,0,"mat-divider",3)(10,co,4,1,"div",4),l()()),a&2){let e=b();d(4),p("ngIf",e.ratingSummary.totalRatings>0),d(),p("ngIf",e.ratingSummary.totalRatings===0),d(3),p("ngForOf",e.getStars(e.ratingSummary.averageRating)),d(),p("ngIf",e.ratingSummary.recentRatings.length>0),d(),p("ngIf",e.ratingSummary.recentRatings.length>0)}}var Vt=class a{constructor(n,e){this.ratingService=n;this.userService=e}userId;ratingSummary=null;raterNames={};ngOnInit(){this.loadRatingSummary()}loadRatingSummary(){return v(this,null,function*(){if(this.userId)try{if(this.ratingSummary=yield this.ratingService.getUserRatingSummary(this.userId),this.ratingSummary.recentRatings.length>0){let n=this.ratingSummary.recentRatings.map(t=>t.rater_id),e=[...new Set(n)];for(let t of e){let i=yield this.userService.getUserById(t);i&&(this.raterNames[t]=i.full_name||i.email)}}}catch(n){console.error("Error loading rating summary:",n)}})}getStars(n){let e=[],t=Math.floor(n),i=n%1>=.5;for(let o=0;o<t;o++)e.push("full");for(i&&e.push("half");e.length<5;)e.push("empty");return e}getFullStars(n){return Array(n).fill(0)}getRaterName(n){return this.raterNames[n]||"Anonymous"}static \u0275fac=function(e){return new(e||a)(W(Re),W(Pt))};static \u0275cmp=x({type:a,selectors:[["app-rating-display"]],inputs:{userId:"userId"},decls:1,vars:1,consts:[[4,"ngIf"],[1,"rating-stars"],[3,"ngClass",4,"ngFor","ngForOf"],["class","rating-divider",4,"ngIf"],["class","recent-ratings",4,"ngIf"],[3,"ngClass"],[1,"rating-divider"],[1,"recent-ratings"],["class","rating-item",4,"ngFor","ngForOf"],[1,"rating-item"],[1,"rating-header"],[1,"rating-stars","small"],["class","small-icon",4,"ngFor","ngForOf"],[1,"rating-info"],[1,"rating-user"],[1,"rating-date"],["class","rating-feedback",4,"ngIf"],[1,"small-icon"],[1,"rating-feedback"]],template:function(e,t){e&1&&w(0,lo,11,5,"mat-card",0),e&2&&p("ngIf",t.ratingSummary)},dependencies:[Ve,Ne,ht,He,Wi,Gi,Ke,qe,We,Ye,$e,Ge,Ue,Qe,Ft,At],styles:[".rating-stars[_ngcontent-%COMP%]{display:flex;margin:10px 0}.rating-stars.small[_ngcontent-%COMP%]{margin:0}.full[_ngcontent-%COMP%], .half[_ngcontent-%COMP%]{color:#ffc107}.empty[_ngcontent-%COMP%]{color:#e0e0e0}.small-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#ffc107}.rating-divider[_ngcontent-%COMP%]{margin:20px 0}.recent-ratings[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:16px;font-weight:500}.rating-item[_ngcontent-%COMP%]{margin-bottom:20px;padding-bottom:10px;border-bottom:1px solid #f0f0f0}.rating-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px}.rating-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end}.rating-user[_ngcontent-%COMP%]{font-weight:500}.rating-date[_ngcontent-%COMP%]{font-size:.8em;color:#757575}.rating-feedback[_ngcontent-%COMP%]{margin:0;font-style:italic;color:#555}"]})};var mo=["userPaginator"],ho=["userSort"];function po(a,n){if(a&1&&(c(0,"div",18)(1,"span",5),h(2,"Fare:"),l(),c(3,"span",7),h(4),l()()),a&2){let e=b(2);d(4),J("$",e.ride.fare,"")}}function uo(a,n){if(a&1&&(c(0,"div",4)(1,"span",5),h(2,"Payment Status:"),l(),c(3,"span",6),h(4),l()()),a&2){let e=b(2);d(3),p("ngClass","payment-"+e.ride.payment_status),d(),J(" ",e.ride.payment_status," ")}}function fo(a,n){if(a&1){let e=$();c(0,"div",19)(1,"button",20),I("click",function(){k(e);let i=b(2);return D(i.markPaymentCompleted())}),c(2,"mat-icon"),h(3,"check_circle"),l(),h(4),l()()}if(a&2){let e=b(2);d(),p("disabled",e.updatingPaymentStatus),d(3),J(" ",e.updatingPaymentStatus?"Updating...":"Mark Payment Completed"," ")}}function _o(a,n){a&1&&(c(0,"mat-error"),h(1,"Fare is required"),l())}function go(a,n){a&1&&(c(0,"mat-error"),h(1,"Fare must be greater than or equal to 0"),l())}function bo(a,n){a&1&&(c(0,"mat-error"),h(1,"Fare must be a valid number with up to 2 decimal places"),l())}function vo(a,n){if(a&1){let e=$();c(0,"div",21)(1,"span",5),h(2,"Fare:"),l(),c(3,"div",22)(4,"mat-form-field",23)(5,"mat-label"),h(6,"Set Fare"),l(),B(7,"input",24),c(8,"span",25),h(9,"$\xA0"),l(),w(10,_o,2,0,"mat-error",1)(11,go,2,0,"mat-error",1)(12,bo,2,0,"mat-error",1),l(),c(13,"button",20),I("click",function(){k(e);let i=b(2);return D(i.updateFare())}),c(14,"mat-icon"),h(15,"save"),l(),h(16),l()()()}if(a&2){let e=b(2);d(7),p("formControl",e.fareControl),d(3),p("ngIf",e.fareControl.hasError("required")),d(),p("ngIf",e.fareControl.hasError("min")),d(),p("ngIf",e.fareControl.hasError("pattern")),d(),p("disabled",e.fareControl.invalid||e.updatingFare),d(3),J(" ",e.updatingFare?"Saving...":"Save"," ")}}function yo(a,n){if(a&1&&(c(0,"div",4)(1,"span",5),h(2,"Distance:"),l(),c(3,"span",7),h(4),l()()),a&2){let e=b(2);d(4),J("",e.ride.distance_miles," miles")}}function wo(a,n){if(a&1&&(c(0,"div",4)(1,"span",5),h(2,"Duration:"),l(),c(3,"span",7),h(4),l()()),a&2){let e=b(2);d(4),J("",e.ride.duration_minutes," minutes")}}function Co(a,n){if(a&1&&(c(0,"div",26),B(1,"app-map-display",27),l()),a&2){let e=b(2);d(),p("origin",e.ride.pickup_location)("destination",e.ride.dropoff_location)}}function xo(a,n){if(a&1&&(c(0,"mat-tab",28),B(1,"app-ride-chat",29),l()),a&2){let e=b(2);d(),p("rideId",e.ride.id)}}function ko(a,n){if(a&1){let e=$();c(0,"div")(1,"app-rating-form",33),I("ratingSubmitted",function(i){k(e);let o=b(3);return D(o.onRatingSubmitted(i))})("ratingCancelled",function(){k(e);let i=b(3);return D(i.onRatingCancelled())}),l()()}if(a&2){let e=b(3);d(),p("ride",e.ride)("userToRate",e.otherUser)}}function Do(a,n){a&1&&(c(0,"div",34)(1,"mat-icon",35),h(2,"check_circle"),l(),c(3,"p"),h(4,"You've already rated this ride. Thank you for your feedback!"),l()())}function Ro(a,n){if(a&1&&(c(0,"mat-tab",30)(1,"div",31),w(2,ko,2,2,"div",32)(3,Do,5,0,"ng-template",null,0,qi),l()()),a&2){let e=tt(4),t=b(2);d(2),p("ngIf",!t.hasRated)("ngIfElse",e)}}function So(a,n){if(a&1&&(c(0,"mat-tab",36),B(1,"app-rating-display",37),l()),a&2){let e=b(2);d(),p("userId",e.otherUser.id)}}function To(a,n){if(a&1){let e=$();c(0,"mat-card")(1,"mat-card-header")(2,"mat-card-title"),h(3,"Ride Details"),l(),c(4,"mat-card-subtitle"),h(5),l(),c(6,"button",2),I("click",function(){k(e);let i=b();return D(i.onClose())}),c(7,"mat-icon"),h(8,"close"),l()()(),c(9,"mat-card-content")(10,"div",3)(11,"div",4)(12,"span",5),h(13,"Status:"),l(),c(14,"span",6),h(15),l()(),c(16,"div",4)(17,"span",5),h(18,"Pickup:"),l(),c(19,"span",7),h(20),l()(),c(21,"div",4)(22,"span",5),h(23,"Destination:"),l(),c(24,"span",7),h(25),l()(),c(26,"div",4)(27,"span",5),h(28,"Rider:"),l(),c(29,"span",7),h(30),l()(),c(31,"div",4)(32,"span",5),h(33,"Driver:"),l(),c(34,"span",7),h(35),l()(),c(36,"div",4)(37,"span",5),h(38,"Pickup Time:"),l(),c(39,"span",7),h(40),l()(),w(41,po,5,1,"div",8)(42,uo,5,2,"div",9)(43,fo,5,2,"div",10),l(),w(44,vo,17,6,"div",11)(45,yo,5,1,"div",9)(46,wo,5,1,"div",9),B(47,"mat-divider",12),c(48,"mat-tab-group")(49,"mat-tab",13),w(50,Co,2,2,"div",14),l(),w(51,xo,2,1,"mat-tab",15)(52,Ro,5,2,"mat-tab",16)(53,So,2,1,"mat-tab",17),l()()()}if(a&2){let e=b();d(5),z(e.formatDate(e.ride.created_at)),d(9),p("ngClass","status-"+e.ride.status),d(),J(" ",e.formatStatus(e.ride.status)," "),d(5),z(e.ride.pickup_location),d(5),z(e.ride.dropoff_location),d(5),z(e.getUserName(e.ride.rider_id)),d(5),z(e.ride.driver_id?e.getUserName(e.ride.driver_id):"No driver assigned"),d(5),z(e.formatDate(e.ride.pickup_time)),d(),p("ngIf",e.ride.fare&&(e.isAdmin||e.isRider)&&!e.isDriver),d(),p("ngIf",e.ride.payment_status),d(),p("ngIf",e.isAdmin&&e.ride.status==="completed"&&(!e.ride.payment_status||e.ride.payment_status!=="completed")),d(),p("ngIf",e.isAdmin&&e.ride.status==="requested"),d(),p("ngIf",e.ride.distance_miles),d(),p("ngIf",e.ride.duration_minutes),d(4),p("ngIf",e.ride.pickup_location&&e.ride.dropoff_location),d(),p("ngIf",e.ride.status!=="requested"&&e.otherUser),d(),p("ngIf",e.canRate&&e.otherUser),d(),p("ngIf",e.otherUser)}}var Qa=class a{constructor(n,e,t,i,o,r){this.rideService=n;this.userService=e;this.authService=t;this.ratingService=i;this.paymentService=o;this.snackBar=r}rideId;onClose=()=>{};paymentRequested=new C;rideUpdated=new C;loadingUsers=!1;ride=null;currentUser=null;otherUser=null;isRider=!1;isDriver=!1;canRate=!1;hasRated=!1;filteredUsers=[];userPaginator;userDisplayedColumns=["email","full_name","role","created_at","status","actions"];userRoleFilter="";userSearchTerm="";userSort;users=[];fareControl=new fa(null,[we.required,we.min(0),we.pattern(/^\d+(\.\d{1,2})?$/)]);isAdmin=!1;updatingFare=!1;updatingPaymentStatus=!1;ngOnInit(){this.loadRideDetails(),this.checkIfAdmin(),this.loadUsers()}checkIfAdmin(){return v(this,null,function*(){let n=yield this.authService.getUserRole();this.isAdmin=n==="admin"})}loadUsers(){return v(this,null,function*(){this.loadingUsers=!0;try{this.users=yield this.userService.getAllUsers(),this.applyUserFilters()}catch(n){console.error("Error loading users:",n),this.snackBar.open("Failed to load users","Close",{duration:3e3})}finally{this.loadingUsers=!1}})}loadRideDetails(){return v(this,null,function*(){if(this.rideId)try{if(this.currentUser=yield this.authService.getCurrentUser(),!this.currentUser||(this.ride=yield this.rideService.getRide(this.rideId),!this.ride))return;this.ride.fare!==void 0&&this.ride.fare!==null&&this.fareControl.setValue(this.ride.fare),this.isRider=this.currentUser.id===this.ride.rider_id,this.isDriver=this.ride.driver_id?this.currentUser.id===this.ride.driver_id:!1;let n=this.isRider?this.ride.driver_id:this.ride.rider_id;n&&(this.otherUser=yield this.userService.getUserById(n)),this.canRate=this.canUserRate(),this.canRate&&this.otherUser&&(this.hasRated=yield this.ratingService.hasUserRated(this.ride.id,this.currentUser.id,this.otherUser.id))}catch(n){console.error("Error loading ride details:",n),this.snackBar.open("Failed to load ride details","Close",{duration:3e3})}})}applyUserFilters(){let n=[...this.users];if(this.userRoleFilter&&(n=n.filter(e=>e.role===this.userRoleFilter)),this.userSearchTerm){let e=this.userSearchTerm.toLowerCase();n=n.filter(t=>t.email.toLowerCase().includes(e)||t.full_name&&t.full_name.toLowerCase().includes(e))}this.filteredUsers=n,this.userPaginator&&this.userPaginator.firstPage(),this.userSort&&this.userSort.sort({id:"",start:"asc",disableClear:!1})}canUserRate(){return!this.ride||!this.currentUser?!1:this.ride.status==="completed"&&(this.currentUser.id===this.ride.rider_id||this.currentUser.id===this.ride.driver_id)&&(this.isRider?!!this.ride.driver_id:!0)}formatDate(n){return new Date(n).toLocaleString()}formatStatus(n){return n.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}onRatingSubmitted(n){return v(this,null,function*(){n&&(this.hasRated=!0,this.snackBar.open("Rating submitted successfully","Close",{duration:3e3}))})}onRatingCancelled(){}getUserName(n){return n?this.users.filter(e=>e.id===n)[0].full_name||"":"N/A"}updateFare(){return v(this,null,function*(){if(!(!this.ride||this.fareControl.invalid)){this.updatingFare=!0;try{let n=this.fareControl.value;if(n===null){this.snackBar.open("Please enter a valid fare amount","Close",{duration:3e3});return}(yield this.rideService.updateRide(this.ride.id,{fare:n}))?(this.snackBar.open("Fare updated successfully","Close",{duration:3e3}),this.ride=yield this.rideService.getRide(this.ride.id),this.ride&&this.rideUpdated.emit(this.ride)):this.snackBar.open("Failed to update fare","Close",{duration:3e3})}catch(n){console.error("Error updating fare:",n),this.snackBar.open("An error occurred while updating the fare","Close",{duration:3e3})}finally{this.updatingFare=!1}}})}viewPayment(n){this.paymentRequested.emit(n)}markPaymentCompleted(){return v(this,null,function*(){if(this.ride){this.updatingPaymentStatus=!0;try{(yield this.paymentService.updateRidePaymentStatus(this.ride.id,"completed"))?(this.snackBar.open("Payment status marked as completed","Close",{duration:3e3}),this.ride=yield this.rideService.getRide(this.ride.id),this.ride&&this.rideUpdated.emit(this.ride)):this.snackBar.open("Failed to update payment status","Close",{duration:3e3})}catch(n){console.error("Error updating payment status:",n),this.snackBar.open("An error occurred while updating the payment status","Close",{duration:3e3})}finally{this.updatingPaymentStatus=!1}}})}static \u0275fac=function(e){return new(e||a)(W(Ia),W(Pt),W(ue),W(Re),W(Bt),W(yt))};static \u0275cmp=x({type:a,selectors:[["app-ride-detail"]],viewQuery:function(e,t){if(e&1&&(O(mo,5),O(ho,5)),e&2){let i;_(i=g())&&(t.userPaginator=i.first),_(i=g())&&(t.userSort=i.first)}},inputs:{rideId:"rideId",onClose:"onClose"},outputs:{paymentRequested:"paymentRequested",rideUpdated:"rideUpdated"},decls:1,vars:1,consts:[["alreadyRated",""],[4,"ngIf"],["mat-icon-button","",1,"close-button",3,"click"],[1,"ride-details"],[1,"detail-row"],[1,"label"],[1,"value","status-badge",3,"ngClass"],[1,"value"],["class","detail-row","style","display: flex; align-items: center;",4,"ngIf"],["class","detail-row",4,"ngIf"],["class","detail-row","style","margin-top: 10px;",4,"ngIf"],["class","detail-row admin-fare-input","stlye","display: flex; align-items: start;",4,"ngIf"],[1,"section-divider"],["label","Map"],["class","map-container",4,"ngIf"],["label","Chat",4,"ngIf"],["label","Rate",4,"ngIf"],["label","Ratings",4,"ngIf"],[1,"detail-row",2,"display","flex","align-items","center"],[1,"detail-row",2,"margin-top","10px"],["mat-raised-button","","color","primary",3,"click","disabled"],["stlye","display: flex; align-items: start;",1,"detail-row","admin-fare-input"],[1,"admin-input-container",2,"display","flex","align-items","start"],["appearance","outline",1,"fare-input"],["matInput","","type","number","step","0.01","min","0","placeholder","Enter fare amount",3,"formControl"],["matPrefix",""],[1,"map-container"],[3,"origin","destination"],["label","Chat"],[3,"rideId"],["label","Rate"],[1,"rating-container"],[4,"ngIf","ngIfElse"],[3,"ratingSubmitted","ratingCancelled","ride","userToRate"],[1,"already-rated-message"],["color","primary"],["label","Ratings"],[3,"userId"]],template:function(e,t){e&1&&w(0,To,54,18,"mat-card",1),e&2&&p("ngIf",t.ride)},dependencies:[Ve,Ne,He,kt,Ct,ga,xt,ya,ba,Ke,qe,We,Ye,$e,Ge,ft,ut,pt,Ue,Qe,Ft,At,Na,di,Ba,wt,Et,Mt,Rt,Dt,xa,ka,It,Ht,Vt,Ma,Ta],styles:["mat-card[_ngcontent-%COMP%]{max-width:800px;margin:180px auto 0;padding:20px}.close-button[_ngcontent-%COMP%]{position:absolute;top:10px;right:10px}.ride-details[_ngcontent-%COMP%]{margin:20px 0}.detail-row[_ngcontent-%COMP%]{display:flex;margin-bottom:10px}.label[_ngcontent-%COMP%]{font-weight:500;width:120px;color:#666}.value[_ngcontent-%COMP%]{flex:1}.status-badge[_ngcontent-%COMP%]{padding:4px 8px;border-radius:4px;font-size:.9em;font-weight:500}.status-requested[_ngcontent-%COMP%]{background-color:#f0f0f0;color:#666}.status-assigned[_ngcontent-%COMP%]{background-color:#e3f2fd;color:#1976d2}.status-in-progress[_ngcontent-%COMP%], .status-completed[_ngcontent-%COMP%]{background-color:#e8f5e9;color:#388e3c}.status-canceled[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f}.payment-pending[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57c00}.payment-completed[_ngcontent-%COMP%], .payment-paid[_ngcontent-%COMP%]{background-color:#e8f5e9;color:#388e3c}.payment-failed[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f}.payment-refunded[_ngcontent-%COMP%]{background-color:#e0f7fa;color:#0097a7}.section-divider[_ngcontent-%COMP%]{margin:20px 0}.map-container[_ngcontent-%COMP%]{margin-top:20px}.rating-container[_ngcontent-%COMP%]{padding:20px 0}.already-rated-message[_ngcontent-%COMP%]{display:flex;align-items:center;padding:20px;background-color:#f5f5f5;border-radius:4px}.already-rated-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:10px}.admin-fare-input[_ngcontent-%COMP%]{margin-top:20px;display:flex;align-items:start;justify-self:start}.mdc-text-field--outlined[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%], .mdc-text-field--no-label[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%]{padding-top:var(8px);padding-bottom:var(8px)}.mat-mdc-form-field-infix[_ngcontent-%COMP%]{min-height:40px!important}.admin-input-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.fare-input[_ngcontent-%COMP%]{width:150px}mat-form-field[_ngcontent-%COMP%]{width:150px}"]})};var Wa=["*",[["mat-chip-avatar"],["","matChipAvatar",""]],[["mat-chip-trailing-icon"],["","matChipRemove",""],["","matChipTrailingIcon",""]]],$a=["*","mat-chip-avatar, [matChipAvatar]","mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]"];function Io(a,n){a&1&&(c(0,"span",3),S(1,1),l())}function Mo(a,n){a&1&&(c(0,"span",6),S(1,2),l())}function Eo(a,n){a&1&&(c(0,"span",3),S(1,1),c(2,"span",8),lt(),c(3,"svg",9),B(4,"path",10),l()()())}function Po(a,n){a&1&&(c(0,"span",6),S(1,2),l())}var Ao=`.mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:"";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:"";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:""}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}
`;var Ya=["*"],Fo=`.mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}
`,fi=new A("mat-chips-default-options",{providedIn:"root",factory:()=>({separatorKeyCodes:[13]})}),Ua=new A("MatChipAvatar"),qa=new A("MatChipTrailingIcon"),Ga=new A("MatChipRemove"),_i=new A("MatChip"),hi=(()=>{class a{_elementRef=s(F);_parentChip=s(_i);isInteractive=!0;_isPrimary=!0;get disabled(){return this._disabled||this._parentChip?.disabled||!1}set disabled(e){this._disabled=e}_disabled=!1;tabIndex=-1;_allowFocusWhenDisabled=!1;_getDisabledAttribute(){return this.disabled&&!this._allowFocusWhenDisabled?"":null}_getTabindex(){return this.disabled&&!this._allowFocusWhenDisabled||!this.isInteractive?null:this.tabIndex.toString()}constructor(){s(Ee).load(Pe),this._elementRef.nativeElement.nodeName==="BUTTON"&&this._elementRef.nativeElement.setAttribute("type","button")}focus(){this._elementRef.nativeElement.focus()}_handleClick(e){!this.disabled&&this.isInteractive&&this._isPrimary&&(e.preventDefault(),this._parentChip._handlePrimaryActionInteraction())}_handleKeydown(e){(e.keyCode===13||e.keyCode===32)&&!this.disabled&&this.isInteractive&&this._isPrimary&&!this._parentChip._isEditing&&(e.preventDefault(),this._parentChip._handlePrimaryActionInteraction())}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","matChipAction",""]],hostAttrs:[1,"mdc-evolution-chip__action","mat-mdc-chip-action"],hostVars:9,hostBindings:function(t,i){t&1&&I("click",function(r){return i._handleClick(r)})("keydown",function(r){return i._handleKeydown(r)}),t&2&&(V("tabindex",i._getTabindex())("disabled",i._getDisabledAttribute())("aria-disabled",i.disabled),T("mdc-evolution-chip__action--primary",i._isPrimary)("mdc-evolution-chip__action--presentational",!i.isInteractive)("mdc-evolution-chip__action--trailing",!i._isPrimary))},inputs:{isInteractive:"isInteractive",disabled:[2,"disabled","disabled",y],tabIndex:[2,"tabIndex","tabIndex",e=>e==null?-1:be(e)],_allowFocusWhenDisabled:"_allowFocusWhenDisabled"}})}return a})();var pi=(()=>{class a{_changeDetectorRef=s(Y);_elementRef=s(F);_ngZone=s(oe);_focusMonitor=s(Me);_globalRippleOptions=s(ii,{optional:!0});_document=s(De);_onFocus=new N;_onBlur=new N;_isBasicChip;role=null;_hasFocusInternal=!1;_pendingFocus;_actionChanges;_animationsDisabled;_allLeadingIcons;_allTrailingIcons;_allRemoveIcons;_hasFocus(){return this._hasFocusInternal}id=s(ve).getId("mat-mdc-chip-");ariaLabel=null;ariaDescription=null;_ariaDescriptionId=`${this.id}-aria-description`;_chipListDisabled=!1;_textElement;get value(){return this._value!==void 0?this._value:this._textElement.textContent.trim()}set value(e){this._value=e}_value;color;removable=!0;highlighted=!1;disableRipple=!1;get disabled(){return this._disabled||this._chipListDisabled}set disabled(e){this._disabled=e}_disabled=!1;removed=new C;destroyed=new C;basicChipAttrName="mat-basic-chip";leadingIcon;trailingIcon;removeIcon;primaryAction;_rippleLoader=s(ta);_injector=s(ge);constructor(){let e=s(Ee);e.load(Pe),e.load(Xi);let t=s(he,{optional:!0});this._animationsDisabled=t==="NoopAnimations",this._monitorFocus(),this._rippleLoader?.configureRipple(this._elementRef.nativeElement,{className:"mat-mdc-chip-ripple",disabled:this._isRippleDisabled()})}ngOnInit(){let e=this._elementRef.nativeElement;this._isBasicChip=e.hasAttribute(this.basicChipAttrName)||e.tagName.toLowerCase()===this.basicChipAttrName}ngAfterViewInit(){this._textElement=this._elementRef.nativeElement.querySelector(".mat-mdc-chip-action-label"),this._pendingFocus&&(this._pendingFocus=!1,this.focus())}ngAfterContentInit(){this._actionChanges=ae(this._allLeadingIcons.changes,this._allTrailingIcons.changes,this._allRemoveIcons.changes).subscribe(()=>this._changeDetectorRef.markForCheck())}ngDoCheck(){this._rippleLoader.setDisabled(this._elementRef.nativeElement,this._isRippleDisabled())}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._rippleLoader?.destroyRipple(this._elementRef.nativeElement),this._actionChanges?.unsubscribe(),this.destroyed.emit({chip:this}),this.destroyed.complete()}remove(){this.removable&&this.removed.emit({chip:this})}_isRippleDisabled(){return this.disabled||this.disableRipple||this._animationsDisabled||this._isBasicChip||!!this._globalRippleOptions?.disabled}_hasTrailingIcon(){return!!(this.trailingIcon||this.removeIcon)}_handleKeydown(e){(e.keyCode===8&&!e.repeat||e.keyCode===46)&&(e.preventDefault(),this.remove())}focus(){this.disabled||(this.primaryAction?this.primaryAction.focus():this._pendingFocus=!0)}_getSourceAction(e){return this._getActions().find(t=>{let i=t._elementRef.nativeElement;return i===e||i.contains(e)})}_getActions(){let e=[];return this.primaryAction&&e.push(this.primaryAction),this.removeIcon&&e.push(this.removeIcon),this.trailingIcon&&e.push(this.trailingIcon),e}_handlePrimaryActionInteraction(){}_monitorFocus(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(e=>{let t=e!==null;t!==this._hasFocusInternal&&(this._hasFocusInternal=t,t?this._onFocus.next({chip:this}):(this._changeDetectorRef.markForCheck(),setTimeout(()=>this._ngZone.run(()=>this._onBlur.next({chip:this})))))})}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=x({type:a,selectors:[["mat-basic-chip"],["","mat-basic-chip",""],["mat-chip"],["","mat-chip",""]],contentQueries:function(t,i,o){if(t&1&&(E(o,Ua,5),E(o,qa,5),E(o,Ga,5),E(o,Ua,5),E(o,qa,5),E(o,Ga,5)),t&2){let r;_(r=g())&&(i.leadingIcon=r.first),_(r=g())&&(i.trailingIcon=r.first),_(r=g())&&(i.removeIcon=r.first),_(r=g())&&(i._allLeadingIcons=r),_(r=g())&&(i._allTrailingIcons=r),_(r=g())&&(i._allRemoveIcons=r)}},viewQuery:function(t,i){if(t&1&&O(hi,5),t&2){let o;_(o=g())&&(i.primaryAction=o.first)}},hostAttrs:[1,"mat-mdc-chip"],hostVars:31,hostBindings:function(t,i){t&1&&I("keydown",function(r){return i._handleKeydown(r)}),t&2&&(dt("id",i.id),V("role",i.role)("aria-label",i.ariaLabel),Le("mat-"+(i.color||"primary")),T("mdc-evolution-chip",!i._isBasicChip)("mdc-evolution-chip--disabled",i.disabled)("mdc-evolution-chip--with-trailing-action",i._hasTrailingIcon())("mdc-evolution-chip--with-primary-graphic",i.leadingIcon)("mdc-evolution-chip--with-primary-icon",i.leadingIcon)("mdc-evolution-chip--with-avatar",i.leadingIcon)("mat-mdc-chip-with-avatar",i.leadingIcon)("mat-mdc-chip-highlighted",i.highlighted)("mat-mdc-chip-disabled",i.disabled)("mat-mdc-basic-chip",i._isBasicChip)("mat-mdc-standard-chip",!i._isBasicChip)("mat-mdc-chip-with-trailing-icon",i._hasTrailingIcon())("_mat-animation-noopable",i._animationsDisabled))},inputs:{role:"role",id:"id",ariaLabel:[0,"aria-label","ariaLabel"],ariaDescription:[0,"aria-description","ariaDescription"],value:"value",color:"color",removable:[2,"removable","removable",y],highlighted:[2,"highlighted","highlighted",y],disableRipple:[2,"disableRipple","disableRipple",y],disabled:[2,"disabled","disabled",y]},outputs:{removed:"removed",destroyed:"destroyed"},exportAs:["matChip"],features:[M([{provide:_i,useExisting:a}])],ngContentSelectors:$a,decls:8,vars:3,consts:[[1,"mat-mdc-chip-focus-overlay"],[1,"mdc-evolution-chip__cell","mdc-evolution-chip__cell--primary"],["matChipAction","",3,"isInteractive"],[1,"mdc-evolution-chip__graphic","mat-mdc-chip-graphic"],[1,"mdc-evolution-chip__text-label","mat-mdc-chip-action-label"],[1,"mat-mdc-chip-primary-focus-indicator","mat-focus-indicator"],[1,"mdc-evolution-chip__cell","mdc-evolution-chip__cell--trailing"]],template:function(t,i){t&1&&(U(Wa),B(0,"span",0),c(1,"span",1)(2,"span",2),w(3,Io,2,0,"span",3),c(4,"span",4),S(5),B(6,"span",5),l()()(),w(7,Mo,2,0,"span",6)),t&2&&(d(2),p("isInteractive",!1),d(),Q(i.leadingIcon?3:-1),d(4),Q(i._hasTrailingIcon()?7:-1))},dependencies:[hi],styles:[`.mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:"";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:"";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:""}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}
`],encapsulation:2,changeDetection:0})}return a})();var Oo=(()=>{class a extends pi{_defaultOptions=s(fi,{optional:!0});chipListSelectable=!0;_chipListMultiple=!1;_chipListHideSingleSelectionIndicator=this._defaultOptions?.hideSingleSelectionIndicator??!1;get selectable(){return this._selectable&&this.chipListSelectable}set selectable(e){this._selectable=e,this._changeDetectorRef.markForCheck()}_selectable=!0;get selected(){return this._selected}set selected(e){this._setSelectedState(e,!1,!0)}_selected=!1;get ariaSelected(){return this.selectable?this.selected.toString():null}basicChipAttrName="mat-basic-chip-option";selectionChange=new C;ngOnInit(){super.ngOnInit(),this.role="presentation"}select(){this._setSelectedState(!0,!1,!0)}deselect(){this._setSelectedState(!1,!1,!0)}selectViaInteraction(){this._setSelectedState(!0,!0,!0)}toggleSelected(e=!1){return this._setSelectedState(!this.selected,e,!0),this.selected}_handlePrimaryActionInteraction(){this.disabled||(this.focus(),this.selectable&&this.toggleSelected(!0))}_hasLeadingGraphic(){return this.leadingIcon?!0:!this._chipListHideSingleSelectionIndicator||this._chipListMultiple}_setSelectedState(e,t,i){e!==this.selected&&(this._selected=e,i&&this.selectionChange.emit({source:this,isUserInput:t,selected:this.selected}),this._changeDetectorRef.markForCheck())}static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275cmp=x({type:a,selectors:[["mat-basic-chip-option"],["","mat-basic-chip-option",""],["mat-chip-option"],["","mat-chip-option",""]],hostAttrs:[1,"mat-mdc-chip","mat-mdc-chip-option"],hostVars:37,hostBindings:function(t,i){t&2&&(dt("id",i.id),V("tabindex",null)("aria-label",null)("aria-description",null)("role",i.role),T("mdc-evolution-chip",!i._isBasicChip)("mdc-evolution-chip--filter",!i._isBasicChip)("mdc-evolution-chip--selectable",!i._isBasicChip)("mat-mdc-chip-selected",i.selected)("mat-mdc-chip-multiple",i._chipListMultiple)("mat-mdc-chip-disabled",i.disabled)("mat-mdc-chip-with-avatar",i.leadingIcon)("mdc-evolution-chip--disabled",i.disabled)("mdc-evolution-chip--selected",i.selected)("mdc-evolution-chip--selecting",!i._animationsDisabled)("mdc-evolution-chip--with-trailing-action",i._hasTrailingIcon())("mdc-evolution-chip--with-primary-icon",i.leadingIcon)("mdc-evolution-chip--with-primary-graphic",i._hasLeadingGraphic())("mdc-evolution-chip--with-avatar",i.leadingIcon)("mat-mdc-chip-highlighted",i.highlighted)("mat-mdc-chip-with-trailing-icon",i._hasTrailingIcon()))},inputs:{selectable:[2,"selectable","selectable",y],selected:[2,"selected","selected",y]},outputs:{selectionChange:"selectionChange"},features:[M([{provide:pi,useExisting:a},{provide:_i,useExisting:a}]),R],ngContentSelectors:$a,decls:10,vars:8,consts:[[1,"mat-mdc-chip-focus-overlay"],[1,"mdc-evolution-chip__cell","mdc-evolution-chip__cell--primary"],["matChipAction","","role","option",3,"_allowFocusWhenDisabled"],[1,"mdc-evolution-chip__graphic","mat-mdc-chip-graphic"],[1,"mdc-evolution-chip__text-label","mat-mdc-chip-action-label"],[1,"mat-mdc-chip-primary-focus-indicator","mat-focus-indicator"],[1,"mdc-evolution-chip__cell","mdc-evolution-chip__cell--trailing"],[1,"cdk-visually-hidden",3,"id"],[1,"mdc-evolution-chip__checkmark"],["viewBox","-2 -3 30 30","focusable","false","aria-hidden","true",1,"mdc-evolution-chip__checkmark-svg"],["fill","none","stroke","currentColor","d","M1.73,12.91 8.1,19.28 22.79,4.59",1,"mdc-evolution-chip__checkmark-path"]],template:function(t,i){t&1&&(U(Wa),B(0,"span",0),c(1,"span",1)(2,"button",2),w(3,Eo,5,0,"span",3),c(4,"span",4),S(5),B(6,"span",5),l()()(),w(7,Po,2,0,"span",6),c(8,"span",7),h(9),l()),t&2&&(d(2),p("_allowFocusWhenDisabled",!0),V("aria-selected",i.ariaSelected)("aria-label",i.ariaLabel)("aria-describedby",i._ariaDescriptionId),d(),Q(i._hasLeadingGraphic()?3:-1),d(4),Q(i._hasTrailingIcon()?7:-1),d(),p("id",i._ariaDescriptionId),d(),z(i.ariaDescription))},dependencies:[hi],styles:[Ao],encapsulation:2,changeDetection:0})}return a})();var Lo=(()=>{class a{_elementRef=s(F);_changeDetectorRef=s(Y);_dir=s(ye,{optional:!0});_lastDestroyedFocusedChipIndex=null;_keyManager;_destroyed=new N;_defaultRole="presentation";get chipFocusChanges(){return this._getChipStream(e=>e._onFocus)}get chipDestroyedChanges(){return this._getChipStream(e=>e.destroyed)}get chipRemovedChanges(){return this._getChipStream(e=>e.removed)}get disabled(){return this._disabled}set disabled(e){this._disabled=e,this._syncChipsState()}_disabled=!1;get empty(){return!this._chips||this._chips.length===0}get role(){return this._explicitRole?this._explicitRole:this.empty?null:this._defaultRole}tabIndex=0;set role(e){this._explicitRole=e}_explicitRole=null;get focused(){return this._hasFocusedChip()}_chips;_chipActions=new Fe;constructor(){}ngAfterViewInit(){this._setUpFocusManagement(),this._trackChipSetChanges(),this._trackDestroyedFocusedChip()}ngOnDestroy(){this._keyManager?.destroy(),this._chipActions.destroy(),this._destroyed.next(),this._destroyed.complete()}_hasFocusedChip(){return this._chips&&this._chips.some(e=>e._hasFocus())}_syncChipsState(){this._chips?.forEach(e=>{e._chipListDisabled=this._disabled,e._changeDetectorRef.markForCheck()})}focus(){}_handleKeydown(e){this._originatesFromChip(e)&&this._keyManager.onKeydown(e)}_isValidIndex(e){return e>=0&&e<this._chips.length}_allowFocusEscape(){let e=this._elementRef.nativeElement.tabIndex;e!==-1&&(this._elementRef.nativeElement.tabIndex=-1,setTimeout(()=>this._elementRef.nativeElement.tabIndex=e))}_getChipStream(e){return this._chips.changes.pipe(ne(null),ct(()=>ae(...this._chips.map(e))))}_originatesFromChip(e){let t=e.target;for(;t&&t!==this._elementRef.nativeElement;){if(t.classList.contains("mat-mdc-chip"))return!0;t=t.parentElement}return!1}_setUpFocusManagement(){this._chips.changes.pipe(ne(this._chips)).subscribe(e=>{let t=[];e.forEach(i=>i._getActions().forEach(o=>t.push(o))),this._chipActions.reset(t),this._chipActions.notifyOnChanges()}),this._keyManager=new je(this._chipActions).withVerticalOrientation().withHorizontalOrientation(this._dir?this._dir.value:"ltr").withHomeAndEnd().skipPredicate(e=>this._skipPredicate(e)),this.chipFocusChanges.pipe(P(this._destroyed)).subscribe(({chip:e})=>{let t=e._getSourceAction(document.activeElement);t&&this._keyManager.updateActiveItem(t)}),this._dir?.change.pipe(P(this._destroyed)).subscribe(e=>this._keyManager.withHorizontalOrientation(e))}_skipPredicate(e){return!e.isInteractive||e.disabled}_trackChipSetChanges(){this._chips.changes.pipe(ne(null),P(this._destroyed)).subscribe(()=>{this.disabled&&Promise.resolve().then(()=>this._syncChipsState()),this._redirectDestroyedChipFocus()})}_trackDestroyedFocusedChip(){this.chipDestroyedChanges.pipe(P(this._destroyed)).subscribe(e=>{let i=this._chips.toArray().indexOf(e.chip);this._isValidIndex(i)&&e.chip._hasFocus()&&(this._lastDestroyedFocusedChipIndex=i)})}_redirectDestroyedChipFocus(){if(this._lastDestroyedFocusedChipIndex!=null){if(this._chips.length){let e=Math.min(this._lastDestroyedFocusedChipIndex,this._chips.length-1),t=this._chips.toArray()[e];t.disabled?this._chips.length===1?this.focus():this._keyManager.setPreviousItemActive():t.focus()}else this.focus();this._lastDestroyedFocusedChipIndex=null}}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=x({type:a,selectors:[["mat-chip-set"]],contentQueries:function(t,i,o){if(t&1&&E(o,pi,5),t&2){let r;_(r=g())&&(i._chips=r)}},hostAttrs:[1,"mat-mdc-chip-set","mdc-evolution-chip-set"],hostVars:1,hostBindings:function(t,i){t&1&&I("keydown",function(r){return i._handleKeydown(r)}),t&2&&V("role",i.role)},inputs:{disabled:[2,"disabled","disabled",y],role:"role",tabIndex:[2,"tabIndex","tabIndex",e=>e==null?0:be(e)]},ngContentSelectors:Ya,decls:2,vars:0,consts:[["role","presentation",1,"mdc-evolution-chip-set__chips"]],template:function(t,i){t&1&&(U(),c(0,"div",0),S(1),l())},styles:[`.mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}
`],encapsulation:2,changeDetection:0})}return a})(),ui=class{source;value;constructor(n,e){this.source=n,this.value=e}},Bo={provide:pa,useExisting:ji(()=>No),multi:!0},No=(()=>{class a extends Lo{_onTouched=()=>{};_onChange=()=>{};_defaultRole="listbox";_defaultOptions=s(fi,{optional:!0});get multiple(){return this._multiple}set multiple(e){this._multiple=e,this._syncListboxProperties()}_multiple=!1;get selected(){let e=this._chips.toArray().filter(t=>t.selected);return this.multiple?e:e[0]}ariaOrientation="horizontal";get selectable(){return this._selectable}set selectable(e){this._selectable=e,this._syncListboxProperties()}_selectable=!0;compareWith=(e,t)=>e===t;required=!1;get hideSingleSelectionIndicator(){return this._hideSingleSelectionIndicator}set hideSingleSelectionIndicator(e){this._hideSingleSelectionIndicator=e,this._syncListboxProperties()}_hideSingleSelectionIndicator=this._defaultOptions?.hideSingleSelectionIndicator??!1;get chipSelectionChanges(){return this._getChipStream(e=>e.selectionChange)}get chipBlurChanges(){return this._getChipStream(e=>e._onBlur)}get value(){return this._value}set value(e){this._chips&&this._chips.length&&this._setSelectionByValue(e,!1),this._value=e}_value;change=new C;_chips=void 0;ngAfterContentInit(){this._chips.changes.pipe(ne(null),P(this._destroyed)).subscribe(()=>{this.value!==void 0&&Promise.resolve().then(()=>{this._setSelectionByValue(this.value,!1)}),this._syncListboxProperties()}),this.chipBlurChanges.pipe(P(this._destroyed)).subscribe(()=>this._blur()),this.chipSelectionChanges.pipe(P(this._destroyed)).subscribe(e=>{this.multiple||this._chips.forEach(t=>{t!==e.source&&t._setSelectedState(!1,!1,!1)}),e.isUserInput&&this._propagateChanges()})}focus(){if(this.disabled)return;let e=this._getFirstSelectedChip();e&&!e.disabled?e.focus():this._chips.length>0?this._keyManager.setFirstItemActive():this._elementRef.nativeElement.focus()}writeValue(e){e!=null?this.value=e:this.value=void 0}registerOnChange(e){this._onChange=e}registerOnTouched(e){this._onTouched=e}setDisabledState(e){this.disabled=e}_setSelectionByValue(e,t=!0){this._clearSelection(),Array.isArray(e)?e.forEach(i=>this._selectValue(i,t)):this._selectValue(e,t)}_blur(){this.disabled||setTimeout(()=>{this.focused||this._markAsTouched()})}_keydown(e){e.keyCode===9&&super._allowFocusEscape()}_markAsTouched(){this._onTouched(),this._changeDetectorRef.markForCheck()}_propagateChanges(){let e=null;Array.isArray(this.selected)?e=this.selected.map(t=>t.value):e=this.selected?this.selected.value:void 0,this._value=e,this.change.emit(new ui(this,e)),this._onChange(e),this._changeDetectorRef.markForCheck()}_clearSelection(e){this._chips.forEach(t=>{t!==e&&t.deselect()})}_selectValue(e,t){let i=this._chips.find(o=>o.value!=null&&this.compareWith(o.value,e));return i&&(t?i.selectViaInteraction():i.select()),i}_syncListboxProperties(){this._chips&&Promise.resolve().then(()=>{this._chips.forEach(e=>{e._chipListMultiple=this.multiple,e.chipListSelectable=this._selectable,e._chipListHideSingleSelectionIndicator=this.hideSingleSelectionIndicator,e._changeDetectorRef.markForCheck()})})}_getFirstSelectedChip(){return Array.isArray(this.selected)?this.selected.length?this.selected[0]:void 0:this.selected}_skipPredicate(e){return!e.isInteractive}static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275cmp=x({type:a,selectors:[["mat-chip-listbox"]],contentQueries:function(t,i,o){if(t&1&&E(o,Oo,5),t&2){let r;_(r=g())&&(i._chips=r)}},hostAttrs:[1,"mdc-evolution-chip-set","mat-mdc-chip-listbox"],hostVars:10,hostBindings:function(t,i){t&1&&I("focus",function(){return i.focus()})("blur",function(){return i._blur()})("keydown",function(r){return i._keydown(r)}),t&2&&(dt("tabIndex",i.disabled||i.empty?-1:i.tabIndex),V("role",i.role)("aria-required",i.role?i.required:null)("aria-disabled",i.disabled.toString())("aria-multiselectable",i.multiple)("aria-orientation",i.ariaOrientation),T("mat-mdc-chip-list-disabled",i.disabled)("mat-mdc-chip-list-required",i.required))},inputs:{multiple:[2,"multiple","multiple",y],ariaOrientation:[0,"aria-orientation","ariaOrientation"],selectable:[2,"selectable","selectable",y],compareWith:"compareWith",required:[2,"required","required",y],hideSingleSelectionIndicator:[2,"hideSingleSelectionIndicator","hideSingleSelectionIndicator",y],value:"value"},outputs:{change:"change"},features:[M([Bo]),R],ngContentSelectors:Ya,decls:2,vars:0,consts:[["role","presentation",1,"mdc-evolution-chip-set__chips"]],template:function(t,i){t&1&&(U(),c(0,"div",0),S(1),l())},styles:[Fo],encapsulation:2,changeDetection:0})}return a})();var Dc=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=X({type:a});static \u0275inj=K({providers:[Da,{provide:fi,useValue:{separatorKeyCodes:[13]}}],imports:[ee,ia,ee]})}return a})();var jo=[[["caption"]],[["colgroup"],["col"]],"*"],Qo=["caption","colgroup, col","*"];function Uo(a,n){a&1&&S(0,2)}function qo(a,n){a&1&&(c(0,"thead",0),Z(1,1),l(),c(2,"tbody",0),Z(3,2)(4,3),l(),c(5,"tfoot",0),Z(6,4),l())}function Go(a,n){a&1&&Z(0,1)(1,2)(2,3)(3,4)}var me=new A("CDK_TABLE");var Gt=(()=>{class a{template=s(G);constructor(){}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","cdkCellDef",""]]})}return a})(),Wt=(()=>{class a{template=s(G);constructor(){}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","cdkHeaderCellDef",""]]})}return a})(),Za=(()=>{class a{template=s(G);constructor(){}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","cdkFooterCellDef",""]]})}return a})(),Ze=(()=>{class a{_table=s(me,{optional:!0});_hasStickyChanged=!1;get name(){return this._name}set name(e){this._setNameInput(e)}_name;get sticky(){return this._sticky}set sticky(e){e!==this._sticky&&(this._sticky=e,this._hasStickyChanged=!0)}_sticky=!1;get stickyEnd(){return this._stickyEnd}set stickyEnd(e){e!==this._stickyEnd&&(this._stickyEnd=e,this._hasStickyChanged=!0)}_stickyEnd=!1;cell;headerCell;footerCell;cssClassFriendlyName;_columnCssClassName;constructor(){}hasStickyChanged(){let e=this._hasStickyChanged;return this.resetStickyChanged(),e}resetStickyChanged(){this._hasStickyChanged=!1}_updateColumnCssClassName(){this._columnCssClassName=[`cdk-column-${this.cssClassFriendlyName}`]}_setNameInput(e){e&&(this._name=e,this.cssClassFriendlyName=e.replace(/[^a-z0-9_-]/gi,"-"),this._updateColumnCssClassName())}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","cdkColumnDef",""]],contentQueries:function(t,i,o){if(t&1&&(E(o,Gt,5),E(o,Wt,5),E(o,Za,5)),t&2){let r;_(r=g())&&(i.cell=r.first),_(r=g())&&(i.headerCell=r.first),_(r=g())&&(i.footerCell=r.first)}},inputs:{name:[0,"cdkColumnDef","name"],sticky:[2,"sticky","sticky",y],stickyEnd:[2,"stickyEnd","stickyEnd",y]},features:[M([{provide:"MAT_SORT_HEADER_COLUMN_DEF",useExisting:a}])]})}return a})(),jt=class{constructor(n,e){e.nativeElement.classList.add(...n._columnCssClassName)}},Ja=(()=>{class a extends jt{constructor(){super(s(Ze),s(F))}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["cdk-header-cell"],["th","cdk-header-cell",""]],hostAttrs:["role","columnheader",1,"cdk-header-cell"],features:[R]})}return a})();var en=(()=>{class a extends jt{constructor(){let e=s(Ze),t=s(F);super(e,t);let i=e._table?._getCellRole();i&&t.nativeElement.setAttribute("role",i)}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["cdk-cell"],["td","cdk-cell",""]],hostAttrs:[1,"cdk-cell"],features:[R]})}return a})(),Qt=class{tasks=[];endTasks=[]},Ut=new A("_COALESCED_STYLE_SCHEDULER"),bi=(()=>{class a{_currentSchedule=null;_ngZone=s(oe);constructor(){}schedule(e){this._createScheduleIfNeeded(),this._currentSchedule.tasks.push(e)}scheduleEnd(e){this._createScheduleIfNeeded(),this._currentSchedule.endTasks.push(e)}_createScheduleIfNeeded(){this._currentSchedule||(this._currentSchedule=new Qt,this._ngZone.runOutsideAngular(()=>queueMicrotask(()=>{for(;this._currentSchedule.tasks.length||this._currentSchedule.endTasks.length;){let e=this._currentSchedule;this._currentSchedule=new Qt;for(let t of e.tasks)t();for(let t of e.endTasks)t()}this._currentSchedule=null})))}static \u0275fac=function(t){return new(t||a)};static \u0275prov=ke({token:a,factory:a.\u0275fac})}return a})();var vi=(()=>{class a{template=s(G);_differs=s(Be);columns;_columnsDiffer;constructor(){}ngOnChanges(e){if(!this._columnsDiffer){let t=e.columns&&e.columns.currentValue||[];this._columnsDiffer=this._differs.find(t).create(),this._columnsDiffer.diff(t)}}getColumnsDiff(){return this._columnsDiffer.diff(this.columns)}extractCellTemplate(e){return this instanceof nt?e.headerCell.template:this instanceof yi?e.footerCell.template:e.cell.template}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,features:[de]})}return a})(),nt=(()=>{class a extends vi{_table=s(me,{optional:!0});_hasStickyChanged=!1;get sticky(){return this._sticky}set sticky(e){e!==this._sticky&&(this._sticky=e,this._hasStickyChanged=!0)}_sticky=!1;constructor(){super(s(G),s(Be))}ngOnChanges(e){super.ngOnChanges(e)}hasStickyChanged(){let e=this._hasStickyChanged;return this.resetStickyChanged(),e}resetStickyChanged(){this._hasStickyChanged=!1}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","cdkHeaderRowDef",""]],inputs:{columns:[0,"cdkHeaderRowDef","columns"],sticky:[2,"cdkHeaderRowDefSticky","sticky",y]},features:[R,de]})}return a})(),yi=(()=>{class a extends vi{_table=s(me,{optional:!0});_hasStickyChanged=!1;get sticky(){return this._sticky}set sticky(e){e!==this._sticky&&(this._sticky=e,this._hasStickyChanged=!0)}_sticky=!1;constructor(){super(s(G),s(Be))}ngOnChanges(e){super.ngOnChanges(e)}hasStickyChanged(){let e=this._hasStickyChanged;return this.resetStickyChanged(),e}resetStickyChanged(){this._hasStickyChanged=!1}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","cdkFooterRowDef",""]],inputs:{columns:[0,"cdkFooterRowDef","columns"],sticky:[2,"cdkFooterRowDefSticky","sticky",y]},features:[R,de]})}return a})(),$t=(()=>{class a extends vi{_table=s(me,{optional:!0});when;constructor(){super(s(G),s(Be))}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","cdkRowDef",""]],inputs:{columns:[0,"cdkRowDefColumns","columns"],when:[0,"cdkRowDefWhen","when"]},features:[R]})}return a})(),Ae=(()=>{class a{_viewContainer=s(re);cells;context;static mostRecentCellOutlet=null;constructor(){a.mostRecentCellOutlet=this}ngOnDestroy(){a.mostRecentCellOutlet===this&&(a.mostRecentCellOutlet=null)}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","cdkCellOutlet",""]]})}return a})(),wi=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275cmp=x({type:a,selectors:[["cdk-header-row"],["tr","cdk-header-row",""]],hostAttrs:["role","row",1,"cdk-header-row"],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(t,i){t&1&&Z(0,0)},dependencies:[Ae],encapsulation:2})}return a})();var Ci=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275cmp=x({type:a,selectors:[["cdk-row"],["tr","cdk-row",""]],hostAttrs:["role","row",1,"cdk-row"],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(t,i){t&1&&Z(0,0)},dependencies:[Ae],encapsulation:2})}return a})(),tn=(()=>{class a{templateRef=s(G);_contentClassName="cdk-no-data-row";constructor(){}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["ng-template","cdkNoDataRow",""]]})}return a})(),Ka=["top","bottom","left","right"],gi=class{_isNativeHtmlTable;_stickCellCss;direction;_coalescedStyleScheduler;_isBrowser;_needsPositionStickyOnElement;_positionListener;_tableInjector;_elemSizeCache=new WeakMap;_resizeObserver=globalThis?.ResizeObserver?new globalThis.ResizeObserver(n=>this._updateCachedSizes(n)):null;_updatedStickyColumnsParamsToReplay=[];_stickyColumnsReplayTimeout=null;_cachedCellWidths=[];_borderCellCss;_destroyed=!1;constructor(n,e,t,i,o=!0,r=!0,m,u){this._isNativeHtmlTable=n,this._stickCellCss=e,this.direction=t,this._coalescedStyleScheduler=i,this._isBrowser=o,this._needsPositionStickyOnElement=r,this._positionListener=m,this._tableInjector=u,this._borderCellCss={top:`${e}-border-elem-top`,bottom:`${e}-border-elem-bottom`,left:`${e}-border-elem-left`,right:`${e}-border-elem-right`}}clearStickyPositioning(n,e){(e.includes("left")||e.includes("right"))&&this._removeFromStickyColumnReplayQueue(n);let t=[];for(let i of n)i.nodeType===i.ELEMENT_NODE&&t.push(i,...Array.from(i.children));this._afterNextRender({write:()=>{for(let i of t)this._removeStickyStyle(i,e)}})}updateStickyColumns(n,e,t,i=!0,o=!0){if(!n.length||!this._isBrowser||!(e.some(ce=>ce)||t.some(ce=>ce))){this._positionListener?.stickyColumnsUpdated({sizes:[]}),this._positionListener?.stickyEndColumnsUpdated({sizes:[]});return}let r=n[0],m=r.children.length,u=this.direction==="rtl",L=u?"right":"left",j=u?"left":"right",q=e.lastIndexOf(!0),fe=t.indexOf(!0),Ce,Pi,Ai;o&&this._updateStickyColumnReplayQueue({rows:[...n],stickyStartStates:[...e],stickyEndStates:[...t]}),this._afterNextRender({earlyRead:()=>{Ce=this._getCellWidths(r,i),Pi=this._getStickyStartColumnPositions(Ce,e),Ai=this._getStickyEndColumnPositions(Ce,t)},write:()=>{for(let ce of n)for(let te=0;te<m;te++){let Fi=ce.children[te];e[te]&&this._addStickyStyle(Fi,L,Pi[te],te===q),t[te]&&this._addStickyStyle(Fi,j,Ai[te],te===fe)}this._positionListener&&Ce.some(ce=>!!ce)&&(this._positionListener.stickyColumnsUpdated({sizes:q===-1?[]:Ce.slice(0,q+1).map((ce,te)=>e[te]?ce:null)}),this._positionListener.stickyEndColumnsUpdated({sizes:fe===-1?[]:Ce.slice(fe).map((ce,te)=>t[te+fe]?ce:null).reverse()}))}})}stickRows(n,e,t){if(!this._isBrowser)return;let i=t==="bottom"?n.slice().reverse():n,o=t==="bottom"?e.slice().reverse():e,r=[],m=[],u=[];this._afterNextRender({earlyRead:()=>{for(let L=0,j=0;L<i.length;L++){if(!o[L])continue;r[L]=j;let q=i[L];u[L]=this._isNativeHtmlTable?Array.from(q.children):[q];let fe=this._retrieveElementSize(q).height;j+=fe,m[L]=fe}},write:()=>{let L=o.lastIndexOf(!0);for(let j=0;j<i.length;j++){if(!o[j])continue;let q=r[j],fe=j===L;for(let Ce of u[j])this._addStickyStyle(Ce,t,q,fe)}t==="top"?this._positionListener?.stickyHeaderRowsUpdated({sizes:m,offsets:r,elements:u}):this._positionListener?.stickyFooterRowsUpdated({sizes:m,offsets:r,elements:u})}})}updateStickyFooterContainer(n,e){this._isNativeHtmlTable&&this._afterNextRender({write:()=>{let t=n.querySelector("tfoot");t&&(e.some(i=>!i)?this._removeStickyStyle(t,["bottom"]):this._addStickyStyle(t,"bottom",0,!1))}})}destroy(){this._stickyColumnsReplayTimeout&&clearTimeout(this._stickyColumnsReplayTimeout),this._resizeObserver?.disconnect(),this._destroyed=!0}_removeStickyStyle(n,e){if(!n.classList.contains(this._stickCellCss))return;for(let i of e)n.style[i]="",n.classList.remove(this._borderCellCss[i]);Ka.some(i=>e.indexOf(i)===-1&&n.style[i])?n.style.zIndex=this._getCalculatedZIndex(n):(n.style.zIndex="",this._needsPositionStickyOnElement&&(n.style.position=""),n.classList.remove(this._stickCellCss))}_addStickyStyle(n,e,t,i){n.classList.add(this._stickCellCss),i&&n.classList.add(this._borderCellCss[e]),n.style[e]=`${t}px`,n.style.zIndex=this._getCalculatedZIndex(n),this._needsPositionStickyOnElement&&(n.style.cssText+="position: -webkit-sticky; position: sticky; ")}_getCalculatedZIndex(n){let e={top:100,bottom:10,left:1,right:1},t=0;for(let i of Ka)n.style[i]&&(t+=e[i]);return t?`${t}`:""}_getCellWidths(n,e=!0){if(!e&&this._cachedCellWidths.length)return this._cachedCellWidths;let t=[],i=n.children;for(let o=0;o<i.length;o++){let r=i[o];t.push(this._retrieveElementSize(r).width)}return this._cachedCellWidths=t,t}_getStickyStartColumnPositions(n,e){let t=[],i=0;for(let o=0;o<n.length;o++)e[o]&&(t[o]=i,i+=n[o]);return t}_getStickyEndColumnPositions(n,e){let t=[],i=0;for(let o=n.length;o>0;o--)e[o]&&(t[o]=i,i+=n[o]);return t}_retrieveElementSize(n){let e=this._elemSizeCache.get(n);if(e)return e;let t=n.getBoundingClientRect(),i={width:t.width,height:t.height};return this._resizeObserver&&(this._elemSizeCache.set(n,i),this._resizeObserver.observe(n,{box:"border-box"})),i}_updateStickyColumnReplayQueue(n){this._removeFromStickyColumnReplayQueue(n.rows),this._stickyColumnsReplayTimeout||this._updatedStickyColumnsParamsToReplay.push(n)}_removeFromStickyColumnReplayQueue(n){let e=new Set(n);for(let t of this._updatedStickyColumnsParamsToReplay)t.rows=t.rows.filter(i=>!e.has(i));this._updatedStickyColumnsParamsToReplay=this._updatedStickyColumnsParamsToReplay.filter(t=>!!t.rows.length)}_updateCachedSizes(n){let e=!1;for(let t of n){let i=t.borderBoxSize?.length?{width:t.borderBoxSize[0].inlineSize,height:t.borderBoxSize[0].blockSize}:{width:t.contentRect.width,height:t.contentRect.height};i.width!==this._elemSizeCache.get(t.target)?.width&&Wo(t.target)&&(e=!0),this._elemSizeCache.set(t.target,i)}e&&this._updatedStickyColumnsParamsToReplay.length&&(this._stickyColumnsReplayTimeout&&clearTimeout(this._stickyColumnsReplayTimeout),this._stickyColumnsReplayTimeout=setTimeout(()=>{if(!this._destroyed){for(let t of this._updatedStickyColumnsParamsToReplay)this.updateStickyColumns(t.rows,t.stickyStartStates,t.stickyEndStates,!0,!1);this._updatedStickyColumnsParamsToReplay=[],this._stickyColumnsReplayTimeout=null}},0))}_afterNextRender(n){this._tableInjector?pe(n,{injector:this._tableInjector}):this._coalescedStyleScheduler.schedule(()=>{n.earlyRead?.(),n.write()})}};function Wo(a){return["cdk-cell","cdk-header-cell","cdk-footer-cell"].some(n=>a.classList.contains(n))}var qt=new A("CDK_SPL");var xi=(()=>{class a{viewContainer=s(re);elementRef=s(F);constructor(){let e=s(me);e._rowOutlet=this,e._outletAssigned()}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","rowOutlet",""]]})}return a})(),ki=(()=>{class a{viewContainer=s(re);elementRef=s(F);constructor(){let e=s(me);e._headerRowOutlet=this,e._outletAssigned()}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","headerRowOutlet",""]]})}return a})(),Di=(()=>{class a{viewContainer=s(re);elementRef=s(F);constructor(){let e=s(me);e._footerRowOutlet=this,e._outletAssigned()}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","footerRowOutlet",""]]})}return a})(),Ri=(()=>{class a{viewContainer=s(re);elementRef=s(F);constructor(){let e=s(me);e._noDataRowOutlet=this,e._outletAssigned()}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","noDataRowOutlet",""]]})}return a})();var Si=(()=>{class a{_differs=s(Be);_changeDetectorRef=s(Y);_elementRef=s(F);_dir=s(ye,{optional:!0});_platform=s(Ie);_viewRepeater=s(at);_coalescedStyleScheduler=s(Ut);_viewportRuler=s(bt);_stickyPositioningListener=s(qt,{optional:!0,skipSelf:!0});_document=s(De);_data;_onDestroy=new N;_renderRows;_renderChangeSubscription;_columnDefsByName=new Map;_rowDefs;_headerRowDefs;_footerRowDefs;_dataDiffer;_defaultRowDef;_customColumnDefs=new Set;_customRowDefs=new Set;_customHeaderRowDefs=new Set;_customFooterRowDefs=new Set;_customNoDataRow;_headerRowDefChanged=!0;_footerRowDefChanged=!0;_stickyColumnStylesNeedReset=!0;_forceRecalculateCellWidths=!0;_cachedRenderRowsMap=new Map;_isNativeHtmlTable;_stickyStyler;stickyCssClass="cdk-table-sticky";needsPositionStickyOnElement=!0;_isServer;_isShowingNoDataRow=!1;_hasAllOutlets=!1;_hasInitialized=!1;_getCellRole(){if(this._cellRoleInternal===void 0){let e=this._elementRef.nativeElement.getAttribute("role");return e==="grid"||e==="treegrid"?"gridcell":"cell"}return this._cellRoleInternal}_cellRoleInternal=void 0;get trackBy(){return this._trackByFn}set trackBy(e){this._trackByFn=e}_trackByFn;get dataSource(){return this._dataSource}set dataSource(e){this._dataSource!==e&&this._switchDataSource(e)}_dataSource;get multiTemplateDataRows(){return this._multiTemplateDataRows}set multiTemplateDataRows(e){this._multiTemplateDataRows=e,this._rowOutlet&&this._rowOutlet.viewContainer.length&&(this._forceRenderDataRows(),this.updateStickyColumnStyles())}_multiTemplateDataRows=!1;get fixedLayout(){return this._fixedLayout}set fixedLayout(e){this._fixedLayout=e,this._forceRecalculateCellWidths=!0,this._stickyColumnStylesNeedReset=!0}_fixedLayout=!1;contentChanged=new C;viewChange=new ie({start:0,end:Number.MAX_VALUE});_rowOutlet;_headerRowOutlet;_footerRowOutlet;_noDataRowOutlet;_contentColumnDefs;_contentRowDefs;_contentHeaderRowDefs;_contentFooterRowDefs;_noDataRow;_injector=s(ge);constructor(){s(new Je("role"),{optional:!0})||this._elementRef.nativeElement.setAttribute("role","table"),this._isServer=!this._platform.isBrowser,this._isNativeHtmlTable=this._elementRef.nativeElement.nodeName==="TABLE",this._dataDiffer=this._differs.find([]).create((t,i)=>this.trackBy?this.trackBy(i.dataIndex,i.data):i)}ngOnInit(){this._setupStickyStyler(),this._viewportRuler.change().pipe(P(this._onDestroy)).subscribe(()=>{this._forceRecalculateCellWidths=!0})}ngAfterContentInit(){this._hasInitialized=!0}ngAfterContentChecked(){this._canRender()&&this._render()}ngOnDestroy(){this._stickyStyler?.destroy(),[this._rowOutlet?.viewContainer,this._headerRowOutlet?.viewContainer,this._footerRowOutlet?.viewContainer,this._cachedRenderRowsMap,this._customColumnDefs,this._customRowDefs,this._customHeaderRowDefs,this._customFooterRowDefs,this._columnDefsByName].forEach(e=>{e?.clear()}),this._headerRowDefs=[],this._footerRowDefs=[],this._defaultRowDef=null,this._onDestroy.next(),this._onDestroy.complete(),gt(this.dataSource)&&this.dataSource.disconnect(this)}renderRows(){this._renderRows=this._getAllRenderRows();let e=this._dataDiffer.diff(this._renderRows);if(!e){this._updateNoDataRow(),this.contentChanged.next();return}let t=this._rowOutlet.viewContainer;this._viewRepeater.applyChanges(e,t,(i,o,r)=>this._getEmbeddedViewArgs(i.item,r),i=>i.item.data,i=>{i.operation===sa.INSERTED&&i.context&&this._renderCellTemplateForItem(i.record.item.rowDef,i.context)}),this._updateRowIndexContext(),e.forEachIdentityChange(i=>{let o=t.get(i.currentIndex);o.context.$implicit=i.item.data}),this._updateNoDataRow(),this.contentChanged.next(),this.updateStickyColumnStyles()}addColumnDef(e){this._customColumnDefs.add(e)}removeColumnDef(e){this._customColumnDefs.delete(e)}addRowDef(e){this._customRowDefs.add(e)}removeRowDef(e){this._customRowDefs.delete(e)}addHeaderRowDef(e){this._customHeaderRowDefs.add(e),this._headerRowDefChanged=!0}removeHeaderRowDef(e){this._customHeaderRowDefs.delete(e),this._headerRowDefChanged=!0}addFooterRowDef(e){this._customFooterRowDefs.add(e),this._footerRowDefChanged=!0}removeFooterRowDef(e){this._customFooterRowDefs.delete(e),this._footerRowDefChanged=!0}setNoDataRow(e){this._customNoDataRow=e}updateStickyHeaderRowStyles(){let e=this._getRenderedRows(this._headerRowOutlet);if(this._isNativeHtmlTable){let i=Xa(this._headerRowOutlet,"thead");i&&(i.style.display=e.length?"":"none")}let t=this._headerRowDefs.map(i=>i.sticky);this._stickyStyler.clearStickyPositioning(e,["top"]),this._stickyStyler.stickRows(e,t,"top"),this._headerRowDefs.forEach(i=>i.resetStickyChanged())}updateStickyFooterRowStyles(){let e=this._getRenderedRows(this._footerRowOutlet);if(this._isNativeHtmlTable){let i=Xa(this._footerRowOutlet,"tfoot");i&&(i.style.display=e.length?"":"none")}let t=this._footerRowDefs.map(i=>i.sticky);this._stickyStyler.clearStickyPositioning(e,["bottom"]),this._stickyStyler.stickRows(e,t,"bottom"),this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement,t),this._footerRowDefs.forEach(i=>i.resetStickyChanged())}updateStickyColumnStyles(){let e=this._getRenderedRows(this._headerRowOutlet),t=this._getRenderedRows(this._rowOutlet),i=this._getRenderedRows(this._footerRowOutlet);(this._isNativeHtmlTable&&!this._fixedLayout||this._stickyColumnStylesNeedReset)&&(this._stickyStyler.clearStickyPositioning([...e,...t,...i],["left","right"]),this._stickyColumnStylesNeedReset=!1),e.forEach((o,r)=>{this._addStickyColumnStyles([o],this._headerRowDefs[r])}),this._rowDefs.forEach(o=>{let r=[];for(let m=0;m<t.length;m++)this._renderRows[m].rowDef===o&&r.push(t[m]);this._addStickyColumnStyles(r,o)}),i.forEach((o,r)=>{this._addStickyColumnStyles([o],this._footerRowDefs[r])}),Array.from(this._columnDefsByName.values()).forEach(o=>o.resetStickyChanged())}_outletAssigned(){!this._hasAllOutlets&&this._rowOutlet&&this._headerRowOutlet&&this._footerRowOutlet&&this._noDataRowOutlet&&(this._hasAllOutlets=!0,this._canRender()&&this._render())}_canRender(){return this._hasAllOutlets&&this._hasInitialized}_render(){this._cacheRowDefs(),this._cacheColumnDefs(),!this._headerRowDefs.length&&!this._footerRowDefs.length&&this._rowDefs.length;let t=this._renderUpdatedColumns()||this._headerRowDefChanged||this._footerRowDefChanged;this._stickyColumnStylesNeedReset=this._stickyColumnStylesNeedReset||t,this._forceRecalculateCellWidths=t,this._headerRowDefChanged&&(this._forceRenderHeaderRows(),this._headerRowDefChanged=!1),this._footerRowDefChanged&&(this._forceRenderFooterRows(),this._footerRowDefChanged=!1),this.dataSource&&this._rowDefs.length>0&&!this._renderChangeSubscription?this._observeRenderChanges():this._stickyColumnStylesNeedReset&&this.updateStickyColumnStyles(),this._checkStickyStates()}_getAllRenderRows(){let e=[],t=this._cachedRenderRowsMap;if(this._cachedRenderRowsMap=new Map,!this._data)return e;for(let i=0;i<this._data.length;i++){let o=this._data[i],r=this._getRenderRowsForData(o,i,t.get(o));this._cachedRenderRowsMap.has(o)||this._cachedRenderRowsMap.set(o,new WeakMap);for(let m=0;m<r.length;m++){let u=r[m],L=this._cachedRenderRowsMap.get(u.data);L.has(u.rowDef)?L.get(u.rowDef).push(u):L.set(u.rowDef,[u]),e.push(u)}}return e}_getRenderRowsForData(e,t,i){return this._getRowDefs(e,t).map(r=>{let m=i&&i.has(r)?i.get(r):[];if(m.length){let u=m.shift();return u.dataIndex=t,u}else return{data:e,rowDef:r,dataIndex:t}})}_cacheColumnDefs(){this._columnDefsByName.clear(),zt(this._getOwnDefs(this._contentColumnDefs),this._customColumnDefs).forEach(t=>{this._columnDefsByName.has(t.name),this._columnDefsByName.set(t.name,t)})}_cacheRowDefs(){this._headerRowDefs=zt(this._getOwnDefs(this._contentHeaderRowDefs),this._customHeaderRowDefs),this._footerRowDefs=zt(this._getOwnDefs(this._contentFooterRowDefs),this._customFooterRowDefs),this._rowDefs=zt(this._getOwnDefs(this._contentRowDefs),this._customRowDefs);let e=this._rowDefs.filter(t=>!t.when);!this.multiTemplateDataRows&&e.length>1,this._defaultRowDef=e[0]}_renderUpdatedColumns(){let e=(r,m)=>{let u=!!m.getColumnsDiff();return r||u},t=this._rowDefs.reduce(e,!1);t&&this._forceRenderDataRows();let i=this._headerRowDefs.reduce(e,!1);i&&this._forceRenderHeaderRows();let o=this._footerRowDefs.reduce(e,!1);return o&&this._forceRenderFooterRows(),t||i||o}_switchDataSource(e){this._data=[],gt(this.dataSource)&&this.dataSource.disconnect(this),this._renderChangeSubscription&&(this._renderChangeSubscription.unsubscribe(),this._renderChangeSubscription=null),e||(this._dataDiffer&&this._dataDiffer.diff([]),this._rowOutlet&&this._rowOutlet.viewContainer.clear()),this._dataSource=e}_observeRenderChanges(){if(!this.dataSource)return;let e;gt(this.dataSource)?e=this.dataSource.connect(this):Bi(this.dataSource)?e=this.dataSource:Array.isArray(this.dataSource)&&(e=xe(this.dataSource)),this._renderChangeSubscription=e.pipe(P(this._onDestroy)).subscribe(t=>{this._data=t||[],this.renderRows()})}_forceRenderHeaderRows(){this._headerRowOutlet.viewContainer.length>0&&this._headerRowOutlet.viewContainer.clear(),this._headerRowDefs.forEach((e,t)=>this._renderRow(this._headerRowOutlet,e,t)),this.updateStickyHeaderRowStyles()}_forceRenderFooterRows(){this._footerRowOutlet.viewContainer.length>0&&this._footerRowOutlet.viewContainer.clear(),this._footerRowDefs.forEach((e,t)=>this._renderRow(this._footerRowOutlet,e,t)),this.updateStickyFooterRowStyles()}_addStickyColumnStyles(e,t){let i=Array.from(t?.columns||[]).map(m=>{let u=this._columnDefsByName.get(m);return u}),o=i.map(m=>m.sticky),r=i.map(m=>m.stickyEnd);this._stickyStyler.updateStickyColumns(e,o,r,!this._fixedLayout||this._forceRecalculateCellWidths)}_getRenderedRows(e){let t=[];for(let i=0;i<e.viewContainer.length;i++){let o=e.viewContainer.get(i);t.push(o.rootNodes[0])}return t}_getRowDefs(e,t){if(this._rowDefs.length==1)return[this._rowDefs[0]];let i=[];if(this.multiTemplateDataRows)i=this._rowDefs.filter(o=>!o.when||o.when(t,e));else{let o=this._rowDefs.find(r=>r.when&&r.when(t,e))||this._defaultRowDef;o&&i.push(o)}return i.length,i}_getEmbeddedViewArgs(e,t){let i=e.rowDef,o={$implicit:e.data};return{templateRef:i.template,context:o,index:t}}_renderRow(e,t,i,o={}){let r=e.viewContainer.createEmbeddedView(t.template,o,i);return this._renderCellTemplateForItem(t,o),r}_renderCellTemplateForItem(e,t){for(let i of this._getCellTemplates(e))Ae.mostRecentCellOutlet&&Ae.mostRecentCellOutlet._viewContainer.createEmbeddedView(i,t);this._changeDetectorRef.markForCheck()}_updateRowIndexContext(){let e=this._rowOutlet.viewContainer;for(let t=0,i=e.length;t<i;t++){let r=e.get(t).context;r.count=i,r.first=t===0,r.last=t===i-1,r.even=t%2===0,r.odd=!r.even,this.multiTemplateDataRows?(r.dataIndex=this._renderRows[t].dataIndex,r.renderIndex=t):r.index=this._renderRows[t].dataIndex}}_getCellTemplates(e){return!e||!e.columns?[]:Array.from(e.columns,t=>{let i=this._columnDefsByName.get(t);return e.extractCellTemplate(i)})}_forceRenderDataRows(){this._dataDiffer.diff([]),this._rowOutlet.viewContainer.clear(),this.renderRows()}_checkStickyStates(){let e=(t,i)=>t||i.hasStickyChanged();this._headerRowDefs.reduce(e,!1)&&this.updateStickyHeaderRowStyles(),this._footerRowDefs.reduce(e,!1)&&this.updateStickyFooterRowStyles(),Array.from(this._columnDefsByName.values()).reduce(e,!1)&&(this._stickyColumnStylesNeedReset=!0,this.updateStickyColumnStyles())}_setupStickyStyler(){let e=this._dir?this._dir.value:"ltr";this._stickyStyler=new gi(this._isNativeHtmlTable,this.stickyCssClass,e,this._coalescedStyleScheduler,this._platform.isBrowser,this.needsPositionStickyOnElement,this._stickyPositioningListener,this._injector),(this._dir?this._dir.change:xe()).pipe(P(this._onDestroy)).subscribe(t=>{this._stickyStyler.direction=t,this.updateStickyColumnStyles()})}_getOwnDefs(e){return e.filter(t=>!t._table||t._table===this)}_updateNoDataRow(){let e=this._customNoDataRow||this._noDataRow;if(!e)return;let t=this._rowOutlet.viewContainer.length===0;if(t===this._isShowingNoDataRow)return;let i=this._noDataRowOutlet.viewContainer;if(t){let o=i.createEmbeddedView(e.templateRef),r=o.rootNodes[0];o.rootNodes.length===1&&r?.nodeType===this._document.ELEMENT_NODE&&(r.setAttribute("role","row"),r.classList.add(e._contentClassName))}else i.clear();this._isShowingNoDataRow=t,this._changeDetectorRef.markForCheck()}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=x({type:a,selectors:[["cdk-table"],["table","cdk-table",""]],contentQueries:function(t,i,o){if(t&1&&(E(o,tn,5),E(o,Ze,5),E(o,$t,5),E(o,nt,5),E(o,yi,5)),t&2){let r;_(r=g())&&(i._noDataRow=r.first),_(r=g())&&(i._contentColumnDefs=r),_(r=g())&&(i._contentRowDefs=r),_(r=g())&&(i._contentHeaderRowDefs=r),_(r=g())&&(i._contentFooterRowDefs=r)}},hostAttrs:[1,"cdk-table"],hostVars:2,hostBindings:function(t,i){t&2&&T("cdk-table-fixed-layout",i.fixedLayout)},inputs:{trackBy:"trackBy",dataSource:"dataSource",multiTemplateDataRows:[2,"multiTemplateDataRows","multiTemplateDataRows",y],fixedLayout:[2,"fixedLayout","fixedLayout",y]},outputs:{contentChanged:"contentChanged"},exportAs:["cdkTable"],features:[M([{provide:me,useExisting:a},{provide:at,useClass:Tt},{provide:Ut,useClass:bi},{provide:qt,useValue:null}])],ngContentSelectors:Qo,decls:5,vars:2,consts:[["role","rowgroup"],["headerRowOutlet",""],["rowOutlet",""],["noDataRowOutlet",""],["footerRowOutlet",""]],template:function(t,i){t&1&&(U(jo),S(0),S(1,1),w(2,Uo,1,0)(3,qo,7,0)(4,Go,4,0)),t&2&&(d(2),Q(i._isServer?2:-1),d(),Q(i._isNativeHtmlTable?3:4))},dependencies:[ki,xi,Ri,Di],styles:[`.cdk-table-fixed-layout{table-layout:fixed}
`],encapsulation:2})}return a})();function zt(a,n){return a.concat(Array.from(n))}function Xa(a,n){let e=n.toUpperCase(),t=a.viewContainer.element.nativeElement;for(;t;){let i=t.nodeType===1?t.nodeName:null;if(i===e)return t;if(i==="TABLE")break;t=t.parentNode}return null}var an=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=X({type:a});static \u0275inj=K({imports:[ma]})}return a})();var $o=[[["caption"]],[["colgroup"],["col"]],"*"],Yo=["caption","colgroup, col","*"];function Ko(a,n){a&1&&S(0,2)}function Xo(a,n){a&1&&(c(0,"thead",0),Z(1,1),l(),c(2,"tbody",2),Z(3,3)(4,4),l(),c(5,"tfoot",0),Z(6,5),l())}function Zo(a,n){a&1&&Z(0,1)(1,3)(2,4)(3,5)}var ml=(()=>{class a extends Si{stickyCssClass="mat-mdc-table-sticky";needsPositionStickyOnElement=!1;static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275cmp=x({type:a,selectors:[["mat-table"],["table","mat-table",""]],hostAttrs:[1,"mat-mdc-table","mdc-data-table__table"],hostVars:2,hostBindings:function(t,i){t&2&&T("mdc-table-fixed-layout",i.fixedLayout)},exportAs:["matTable"],features:[M([{provide:Si,useExisting:a},{provide:me,useExisting:a},{provide:Ut,useClass:bi},{provide:at,useClass:Tt},{provide:qt,useValue:null}]),R],ngContentSelectors:Yo,decls:5,vars:2,consts:[["role","rowgroup"],["headerRowOutlet",""],["role","rowgroup",1,"mdc-data-table__content"],["rowOutlet",""],["noDataRowOutlet",""],["footerRowOutlet",""]],template:function(t,i){t&1&&(U($o),S(0),S(1,1),w(2,Ko,1,0)(3,Xo,7,0)(4,Zo,4,0)),t&2&&(d(2),Q(i._isServer?2:-1),d(),Q(i._isNativeHtmlTable?3:4))},dependencies:[ki,xi,Ri,Di],styles:[`.mat-mdc-table-sticky{position:sticky !important}mat-table{display:block}mat-header-row{min-height:var(--mat-table-header-container-height, 56px)}mat-row{min-height:var(--mat-table-row-item-container-height, 52px)}mat-footer-row{min-height:var(--mat-table-footer-container-height, 52px)}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{min-width:100%;border:0;border-spacing:0;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color, var(--mat-sys-surface))}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell{text-align:right}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-header-headline-font, var(--mat-sys-title-small-font, Roboto, sans-serif));line-height:var(--mat-table-header-headline-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-table-header-headline-size, var(--mat-sys-title-small-size, 14px));font-weight:var(--mat-table-header-headline-weight, var(--mat-sys-title-small-weight, 500))}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-row-item-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-row-item-label-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-row-item-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-footer-supporting-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-footer-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-footer-supporting-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-footer-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-table-footer-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking, var(--mat-sys-title-small-tracking));font-weight:inherit;line-height:inherit;box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mat-mdc-header-cell{text-align:right}.mdc-data-table__row:last-child>.mat-mdc-header-cell{border-bottom:none}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking));line-height:inherit}.mdc-data-table__row:last-child>.mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking))}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}
`],encapsulation:2})}return a})(),hl=(()=>{class a extends Gt{static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275dir=f({type:a,selectors:[["","matCellDef",""]],features:[M([{provide:Gt,useExisting:a}]),R]})}return a})(),pl=(()=>{class a extends Wt{static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275dir=f({type:a,selectors:[["","matHeaderCellDef",""]],features:[M([{provide:Wt,useExisting:a}]),R]})}return a})();var ul=(()=>{class a extends Ze{get name(){return this._name}set name(e){this._setNameInput(e)}_updateColumnCssClassName(){super._updateColumnCssClassName(),this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`)}static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275dir=f({type:a,selectors:[["","matColumnDef",""]],inputs:{name:[0,"matColumnDef","name"]},features:[M([{provide:Ze,useExisting:a},{provide:"MAT_SORT_HEADER_COLUMN_DEF",useExisting:a}]),R]})}return a})(),fl=(()=>{class a extends Ja{static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275dir=f({type:a,selectors:[["mat-header-cell"],["th","mat-header-cell",""]],hostAttrs:["role","columnheader",1,"mat-mdc-header-cell","mdc-data-table__header-cell"],features:[R]})}return a})();var _l=(()=>{class a extends en{static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275dir=f({type:a,selectors:[["mat-cell"],["td","mat-cell",""]],hostAttrs:[1,"mat-mdc-cell","mdc-data-table__cell"],features:[R]})}return a})();var gl=(()=>{class a extends nt{static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275dir=f({type:a,selectors:[["","matHeaderRowDef",""]],inputs:{columns:[0,"matHeaderRowDef","columns"],sticky:[2,"matHeaderRowDefSticky","sticky",y]},features:[M([{provide:nt,useExisting:a}]),R]})}return a})();var bl=(()=>{class a extends $t{static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275dir=f({type:a,selectors:[["","matRowDef",""]],inputs:{columns:[0,"matRowDefColumns","columns"],when:[0,"matRowDefWhen","when"]},features:[M([{provide:$t,useExisting:a}]),R]})}return a})(),vl=(()=>{class a extends wi{static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275cmp=x({type:a,selectors:[["mat-header-row"],["tr","mat-header-row",""]],hostAttrs:["role","row",1,"mat-mdc-header-row","mdc-data-table__header-row"],exportAs:["matHeaderRow"],features:[M([{provide:wi,useExisting:a}]),R],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(t,i){t&1&&Z(0,0)},dependencies:[Ae],encapsulation:2})}return a})();var yl=(()=>{class a extends Ci{static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275cmp=x({type:a,selectors:[["mat-row"],["tr","mat-row",""]],hostAttrs:["role","row",1,"mat-mdc-row","mdc-data-table__row"],exportAs:["matRow"],features:[M([{provide:Ci,useExisting:a}]),R],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(t,i){t&1&&Z(0,0)},dependencies:[Ae],encapsulation:2})}return a})();var wl=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=X({type:a});static \u0275inj=K({imports:[ee,an,ee]})}return a})(),Jo=9007199254740991,nn=class extends ra{_data;_renderData=new ie([]);_filter=new ie("");_internalPageChanges=new N;_renderChangesSubscription=null;filteredData;get data(){return this._data.value}set data(n){n=Array.isArray(n)?n:[],this._data.next(n),this._renderChangesSubscription||this._filterData(n)}get filter(){return this._filter.value}set filter(n){this._filter.next(n),this._renderChangesSubscription||this._filterData(this.data)}get sort(){return this._sort}set sort(n){this._sort=n,this._updateChangeSubscription()}_sort;get paginator(){return this._paginator}set paginator(n){this._paginator=n,this._updateChangeSubscription()}_paginator;sortingDataAccessor=(n,e)=>{let t=n[e];if(Yi(t)){let i=Number(t);return i<Jo?i:t}return t};sortData=(n,e)=>{let t=e.active,i=e.direction;return!t||i==""?n:n.sort((o,r)=>{let m=this.sortingDataAccessor(o,t),u=this.sortingDataAccessor(r,t),L=typeof m,j=typeof u;L!==j&&(L==="number"&&(m+=""),j==="number"&&(u+=""));let q=0;return m!=null&&u!=null?m>u?q=1:m<u&&(q=-1):m!=null?q=1:u!=null&&(q=-1),q*(i=="asc"?1:-1)})};filterPredicate=(n,e)=>{let t=e.trim().toLowerCase();return Object.values(n).some(i=>`${i}`.toLowerCase().includes(t))};constructor(n=[]){super(),this._data=new ie(n),this._updateChangeSubscription()}_updateChangeSubscription(){let n=this._sort?ae(this._sort.sortChange,this._sort.initialized):xe(null),e=this._paginator?ae(this._paginator.page,this._internalPageChanges,this._paginator.initialized):xe(null),t=this._data,i=st([t,this._filter]).pipe(rt(([m])=>this._filterData(m))),o=st([i,n]).pipe(rt(([m])=>this._orderData(m))),r=st([o,e]).pipe(rt(([m])=>this._pageData(m)));this._renderChangesSubscription?.unsubscribe(),this._renderChangesSubscription=r.subscribe(m=>this._renderData.next(m))}_filterData(n){return this.filteredData=this.filter==null||this.filter===""?n:n.filter(e=>this.filterPredicate(e,this.filter)),this.paginator&&this._updatePaginator(this.filteredData.length),this.filteredData}_orderData(n){return this.sort?this.sortData(n.slice(),this.sort):n}_pageData(n){if(!this.paginator)return n;let e=this.paginator.pageIndex*this.paginator.pageSize;return n.slice(e,e+this.paginator.pageSize)}_updatePaginator(n){Promise.resolve().then(()=>{let e=this.paginator;if(e&&(e.length=n,e.pageIndex>0)){let t=Math.ceil(e.length/e.pageSize)-1||0,i=Math.min(e.pageIndex,t);i!==e.pageIndex&&(e.pageIndex=i,this._internalPageChanges.next())}})}connect(){return this._renderChangesSubscription||this._updateChangeSubscription(),this._renderData}disconnect(){this._renderChangesSubscription?.unsubscribe(),this._renderChangesSubscription=null}};var tr=["tooltip"],Ti=20;var Ii=new A("mat-tooltip-scroll-strategy",{providedIn:"root",factory:()=>{let a=s(vt);return()=>a.scrollStrategies.reposition({scrollThrottle:Ti})}});function sn(a){return()=>a.scrollStrategies.reposition({scrollThrottle:Ti})}var cn={provide:Ii,deps:[vt],useFactory:sn};function ln(){return{showDelay:0,hideDelay:0,touchendHideDelay:1500}}var dn=new A("mat-tooltip-default-options",{providedIn:"root",factory:ln});var on="tooltip-panel",rn=$i({passive:!0}),ir=8,ar=8,nr=24,or=200,rr=(()=>{class a{_elementRef=s(F);_ngZone=s(oe);_platform=s(Ie);_ariaDescriber=s(ea);_focusMonitor=s(Me);_dir=s(ye);_injector=s(ge);_viewContainerRef=s(re);_defaultOptions=s(dn,{optional:!0});_overlayRef;_tooltipInstance;_portal;_position="below";_positionAtOrigin=!1;_disabled=!1;_tooltipClass;_viewInitialized=!1;_pointerExitEventsInitialized=!1;_tooltipComponent=mn;_viewportMargin=8;_currentPosition;_cssClassPrefix="mat-mdc";_ariaDescriptionPending;_dirSubscribed=!1;get position(){return this._position}set position(e){e!==this._position&&(this._position=e,this._overlayRef&&(this._updatePosition(this._overlayRef),this._tooltipInstance?.show(0),this._overlayRef.updatePosition()))}get positionAtOrigin(){return this._positionAtOrigin}set positionAtOrigin(e){this._positionAtOrigin=ti(e),this._detach(),this._overlayRef=null}get disabled(){return this._disabled}set disabled(e){let t=ti(e);this._disabled!==t&&(this._disabled=t,t?this.hide(0):this._setupPointerEnterEventsIfNeeded(),this._syncAriaDescription(this.message))}get showDelay(){return this._showDelay}set showDelay(e){this._showDelay=ei(e)}_showDelay;get hideDelay(){return this._hideDelay}set hideDelay(e){this._hideDelay=ei(e),this._tooltipInstance&&(this._tooltipInstance._mouseLeaveHideDelay=this._hideDelay)}_hideDelay;touchGestures="auto";get message(){return this._message}set message(e){let t=this._message;this._message=e!=null?String(e).trim():"",!this._message&&this._isTooltipVisible()?this.hide(0):(this._setupPointerEnterEventsIfNeeded(),this._updateTooltipMessage()),this._syncAriaDescription(t)}_message="";get tooltipClass(){return this._tooltipClass}set tooltipClass(e){this._tooltipClass=e,this._tooltipInstance&&this._setTooltipClass(this._tooltipClass)}_passiveListeners=[];_touchstartTimeout=null;_destroyed=new N;_isDestroyed=!1;constructor(){let e=this._defaultOptions;e&&(this._showDelay=e.showDelay,this._hideDelay=e.hideDelay,e.position&&(this.position=e.position),e.positionAtOrigin&&(this.positionAtOrigin=e.positionAtOrigin),e.touchGestures&&(this.touchGestures=e.touchGestures),e.tooltipClass&&(this.tooltipClass=e.tooltipClass)),this._viewportMargin=ir}ngAfterViewInit(){this._viewInitialized=!0,this._setupPointerEnterEventsIfNeeded(),this._focusMonitor.monitor(this._elementRef).pipe(P(this._destroyed)).subscribe(e=>{e?e==="keyboard"&&this._ngZone.run(()=>this.show()):this._ngZone.run(()=>this.hide(0))})}ngOnDestroy(){let e=this._elementRef.nativeElement;this._touchstartTimeout&&clearTimeout(this._touchstartTimeout),this._overlayRef&&(this._overlayRef.dispose(),this._tooltipInstance=null),this._passiveListeners.forEach(([t,i])=>{e.removeEventListener(t,i,rn)}),this._passiveListeners.length=0,this._destroyed.next(),this._destroyed.complete(),this._isDestroyed=!0,this._ariaDescriber.removeDescription(e,this.message,"tooltip"),this._focusMonitor.stopMonitoring(e)}show(e=this.showDelay,t){if(this.disabled||!this.message||this._isTooltipVisible()){this._tooltipInstance?._cancelPendingAnimations();return}let i=this._createOverlay(t);this._detach(),this._portal=this._portal||new aa(this._tooltipComponent,this._viewContainerRef);let o=this._tooltipInstance=i.attach(this._portal).instance;o._triggerElement=this._elementRef.nativeElement,o._mouseLeaveHideDelay=this._hideDelay,o.afterHidden().pipe(P(this._destroyed)).subscribe(()=>this._detach()),this._setTooltipClass(this._tooltipClass),this._updateTooltipMessage(),o.show(e)}hide(e=this.hideDelay){let t=this._tooltipInstance;t&&(t.isVisible()?t.hide(e):(t._cancelPendingAnimations(),this._detach()))}toggle(e){this._isTooltipVisible()?this.hide():this.show(void 0,e)}_isTooltipVisible(){return!!this._tooltipInstance&&this._tooltipInstance.isVisible()}_createOverlay(e){if(this._overlayRef){let r=this._overlayRef.getConfig().positionStrategy;if((!this.positionAtOrigin||!e)&&r._origin instanceof F)return this._overlayRef;this._detach()}let t=this._injector.get(ca).getAncestorScrollContainers(this._elementRef),i=this._injector.get(vt),o=i.position().flexibleConnectedTo(this.positionAtOrigin?e||this._elementRef:this._elementRef).withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`).withFlexibleDimensions(!1).withViewportMargin(this._viewportMargin).withScrollableContainers(t);return o.positionChanges.pipe(P(this._destroyed)).subscribe(r=>{this._updateCurrentPositionClass(r.connectionPair),this._tooltipInstance&&r.scrollableViewProperties.isOverlayClipped&&this._tooltipInstance.isVisible()&&this._ngZone.run(()=>this.hide(0))}),this._overlayRef=i.create({direction:this._dir,positionStrategy:o,panelClass:`${this._cssClassPrefix}-${on}`,scrollStrategy:this._injector.get(Ii)()}),this._updatePosition(this._overlayRef),this._overlayRef.detachments().pipe(P(this._destroyed)).subscribe(()=>this._detach()),this._overlayRef.outsidePointerEvents().pipe(P(this._destroyed)).subscribe(()=>this._tooltipInstance?._handleBodyInteraction()),this._overlayRef.keydownEvents().pipe(P(this._destroyed)).subscribe(r=>{this._isTooltipVisible()&&r.keyCode===27&&!ze(r)&&(r.preventDefault(),r.stopPropagation(),this._ngZone.run(()=>this.hide(0)))}),this._defaultOptions?.disableTooltipInteractivity&&this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`),this._dirSubscribed||(this._dirSubscribed=!0,this._dir.change.pipe(P(this._destroyed)).subscribe(()=>{this._overlayRef&&this._updatePosition(this._overlayRef)})),this._overlayRef}_detach(){this._overlayRef&&this._overlayRef.hasAttached()&&this._overlayRef.detach(),this._tooltipInstance=null}_updatePosition(e){let t=e.getConfig().positionStrategy,i=this._getOrigin(),o=this._getOverlayPosition();t.withPositions([this._addOffset(Se(Se({},i.main),o.main)),this._addOffset(Se(Se({},i.fallback),o.fallback))])}_addOffset(e){let t=ar,i=!this._dir||this._dir.value=="ltr";return e.originY==="top"?e.offsetY=-t:e.originY==="bottom"?e.offsetY=t:e.originX==="start"?e.offsetX=i?-t:t:e.originX==="end"&&(e.offsetX=i?t:-t),e}_getOrigin(){let e=!this._dir||this._dir.value=="ltr",t=this.position,i;t=="above"||t=="below"?i={originX:"center",originY:t=="above"?"top":"bottom"}:t=="before"||t=="left"&&e||t=="right"&&!e?i={originX:"start",originY:"center"}:(t=="after"||t=="right"&&e||t=="left"&&!e)&&(i={originX:"end",originY:"center"});let{x:o,y:r}=this._invertPosition(i.originX,i.originY);return{main:i,fallback:{originX:o,originY:r}}}_getOverlayPosition(){let e=!this._dir||this._dir.value=="ltr",t=this.position,i;t=="above"?i={overlayX:"center",overlayY:"bottom"}:t=="below"?i={overlayX:"center",overlayY:"top"}:t=="before"||t=="left"&&e||t=="right"&&!e?i={overlayX:"end",overlayY:"center"}:(t=="after"||t=="right"&&e||t=="left"&&!e)&&(i={overlayX:"start",overlayY:"center"});let{x:o,y:r}=this._invertPosition(i.overlayX,i.overlayY);return{main:i,fallback:{overlayX:o,overlayY:r}}}_updateTooltipMessage(){this._tooltipInstance&&(this._tooltipInstance.message=this.message,this._tooltipInstance._markForCheck(),pe(()=>{this._tooltipInstance&&this._overlayRef.updatePosition()},{injector:this._injector}))}_setTooltipClass(e){this._tooltipInstance&&(this._tooltipInstance.tooltipClass=e,this._tooltipInstance._markForCheck())}_invertPosition(e,t){return this.position==="above"||this.position==="below"?t==="top"?t="bottom":t==="bottom"&&(t="top"):e==="end"?e="start":e==="start"&&(e="end"),{x:e,y:t}}_updateCurrentPositionClass(e){let{overlayY:t,originX:i,originY:o}=e,r;if(t==="center"?this._dir&&this._dir.value==="rtl"?r=i==="end"?"left":"right":r=i==="start"?"left":"right":r=t==="bottom"&&o==="top"?"above":"below",r!==this._currentPosition){let m=this._overlayRef;if(m){let u=`${this._cssClassPrefix}-${on}-`;m.removePanelClass(u+this._currentPosition),m.addPanelClass(u+r)}this._currentPosition=r}}_setupPointerEnterEventsIfNeeded(){this._disabled||!this.message||!this._viewInitialized||this._passiveListeners.length||(this._platformSupportsMouseEvents()?this._passiveListeners.push(["mouseenter",e=>{this._setupPointerExitEventsIfNeeded();let t;e.x!==void 0&&e.y!==void 0&&(t=e),this.show(void 0,t)}]):this.touchGestures!=="off"&&(this._disableNativeGesturesIfNecessary(),this._passiveListeners.push(["touchstart",e=>{let t=e.targetTouches?.[0],i=t?{x:t.clientX,y:t.clientY}:void 0;this._setupPointerExitEventsIfNeeded(),this._touchstartTimeout&&clearTimeout(this._touchstartTimeout);let o=500;this._touchstartTimeout=setTimeout(()=>{this._touchstartTimeout=null,this.show(void 0,i)},this._defaultOptions?.touchLongPressShowDelay??o)}])),this._addListeners(this._passiveListeners))}_setupPointerExitEventsIfNeeded(){if(this._pointerExitEventsInitialized)return;this._pointerExitEventsInitialized=!0;let e=[];if(this._platformSupportsMouseEvents())e.push(["mouseleave",t=>{let i=t.relatedTarget;(!i||!this._overlayRef?.overlayElement.contains(i))&&this.hide()}],["wheel",t=>this._wheelListener(t)]);else if(this.touchGestures!=="off"){this._disableNativeGesturesIfNecessary();let t=()=>{this._touchstartTimeout&&clearTimeout(this._touchstartTimeout),this.hide(this._defaultOptions?.touchendHideDelay)};e.push(["touchend",t],["touchcancel",t])}this._addListeners(e),this._passiveListeners.push(...e)}_addListeners(e){e.forEach(([t,i])=>{this._elementRef.nativeElement.addEventListener(t,i,rn)})}_platformSupportsMouseEvents(){return!this._platform.IOS&&!this._platform.ANDROID}_wheelListener(e){if(this._isTooltipVisible()){let t=this._injector.get(De).elementFromPoint(e.clientX,e.clientY),i=this._elementRef.nativeElement;t!==i&&!i.contains(t)&&this.hide()}}_disableNativeGesturesIfNecessary(){let e=this.touchGestures;if(e!=="off"){let t=this._elementRef.nativeElement,i=t.style;(e==="on"||t.nodeName!=="INPUT"&&t.nodeName!=="TEXTAREA")&&(i.userSelect=i.msUserSelect=i.webkitUserSelect=i.MozUserSelect="none"),(e==="on"||!t.draggable)&&(i.webkitUserDrag="none"),i.touchAction="none",i.webkitTapHighlightColor="transparent"}}_syncAriaDescription(e){this._ariaDescriptionPending||(this._ariaDescriptionPending=!0,this._ariaDescriber.removeDescription(this._elementRef.nativeElement,e,"tooltip"),this._isDestroyed||pe({write:()=>{this._ariaDescriptionPending=!1,this.message&&!this.disabled&&this._ariaDescriber.describe(this._elementRef.nativeElement,this.message,"tooltip")}},{injector:this._injector}))}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["","matTooltip",""]],hostAttrs:[1,"mat-mdc-tooltip-trigger"],hostVars:2,hostBindings:function(t,i){t&2&&T("mat-mdc-tooltip-disabled",i.disabled)},inputs:{position:[0,"matTooltipPosition","position"],positionAtOrigin:[0,"matTooltipPositionAtOrigin","positionAtOrigin"],disabled:[0,"matTooltipDisabled","disabled"],showDelay:[0,"matTooltipShowDelay","showDelay"],hideDelay:[0,"matTooltipHideDelay","hideDelay"],touchGestures:[0,"matTooltipTouchGestures","touchGestures"],message:[0,"matTooltip","message"],tooltipClass:[0,"matTooltipClass","tooltipClass"]},exportAs:["matTooltip"]})}return a})(),mn=(()=>{class a{_changeDetectorRef=s(Y);_elementRef=s(F);_isMultiline=!1;message;tooltipClass;_showTimeoutId;_hideTimeoutId;_triggerElement;_mouseLeaveHideDelay;_animationsDisabled;_tooltip;_closeOnInteraction=!1;_isVisible=!1;_onHide=new N;_showAnimation="mat-mdc-tooltip-show";_hideAnimation="mat-mdc-tooltip-hide";constructor(){let e=s(he,{optional:!0});this._animationsDisabled=e==="NoopAnimations"}show(e){this._hideTimeoutId!=null&&clearTimeout(this._hideTimeoutId),this._showTimeoutId=setTimeout(()=>{this._toggleVisibility(!0),this._showTimeoutId=void 0},e)}hide(e){this._showTimeoutId!=null&&clearTimeout(this._showTimeoutId),this._hideTimeoutId=setTimeout(()=>{this._toggleVisibility(!1),this._hideTimeoutId=void 0},e)}afterHidden(){return this._onHide}isVisible(){return this._isVisible}ngOnDestroy(){this._cancelPendingAnimations(),this._onHide.complete(),this._triggerElement=null}_handleBodyInteraction(){this._closeOnInteraction&&this.hide(0)}_markForCheck(){this._changeDetectorRef.markForCheck()}_handleMouseLeave({relatedTarget:e}){(!e||!this._triggerElement.contains(e))&&(this.isVisible()?this.hide(this._mouseLeaveHideDelay):this._finalizeAnimation(!1))}_onShow(){this._isMultiline=this._isTooltipMultiline(),this._markForCheck()}_isTooltipMultiline(){let e=this._elementRef.nativeElement.getBoundingClientRect();return e.height>nr&&e.width>=or}_handleAnimationEnd({animationName:e}){(e===this._showAnimation||e===this._hideAnimation)&&this._finalizeAnimation(e===this._showAnimation)}_cancelPendingAnimations(){this._showTimeoutId!=null&&clearTimeout(this._showTimeoutId),this._hideTimeoutId!=null&&clearTimeout(this._hideTimeoutId),this._showTimeoutId=this._hideTimeoutId=void 0}_finalizeAnimation(e){e?this._closeOnInteraction=!0:this.isVisible()||this._onHide.next()}_toggleVisibility(e){let t=this._tooltip.nativeElement,i=this._showAnimation,o=this._hideAnimation;if(t.classList.remove(e?o:i),t.classList.add(e?i:o),this._isVisible!==e&&(this._isVisible=e,this._changeDetectorRef.markForCheck()),e&&!this._animationsDisabled&&typeof getComputedStyle=="function"){let r=getComputedStyle(t);(r.getPropertyValue("animation-duration")==="0s"||r.getPropertyValue("animation-name")==="none")&&(this._animationsDisabled=!0)}e&&this._onShow(),this._animationsDisabled&&(t.classList.add("_mat-animation-noopable"),this._finalizeAnimation(e))}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=x({type:a,selectors:[["mat-tooltip-component"]],viewQuery:function(t,i){if(t&1&&O(tr,7),t&2){let o;_(o=g())&&(i._tooltip=o.first)}},hostAttrs:["aria-hidden","true"],hostBindings:function(t,i){t&1&&I("mouseleave",function(r){return i._handleMouseLeave(r)})},decls:4,vars:4,consts:[["tooltip",""],[1,"mdc-tooltip","mat-mdc-tooltip",3,"animationend","ngClass"],[1,"mat-mdc-tooltip-surface","mdc-tooltip__surface"]],template:function(t,i){if(t&1){let o=$();c(0,"div",1,0),I("animationend",function(m){return k(o),D(i._handleAnimationEnd(m))}),c(2,"div",2),h(3),l()()}t&2&&(T("mdc-tooltip--multiline",i._isMultiline),p("ngClass",i.tooltipClass),d(3),z(i.message))},dependencies:[Ne],styles:[`.mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:"";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:"";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}
`],encapsulation:2,changeDetection:0})}return a})(),sr=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=X({type:a});static \u0275inj=K({providers:[cn],imports:[Ji,ha,ee,ee,da]})}return a})();var Mi=new A("CdkAccordion"),hn=(()=>{class a{_stateChanges=new N;_openCloseAllActions=new N;id=s(ve).getId("cdk-accordion-");multi=!1;openAll(){this.multi&&this._openCloseAllActions.next(!0)}closeAll(){this._openCloseAllActions.next(!1)}ngOnChanges(e){this._stateChanges.next(e)}ngOnDestroy(){this._stateChanges.complete(),this._openCloseAllActions.complete()}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["cdk-accordion"],["","cdkAccordion",""]],inputs:{multi:[2,"multi","multi",y]},exportAs:["cdkAccordion"],features:[M([{provide:Mi,useExisting:a}]),de]})}return a})(),pn=(()=>{class a{accordion=s(Mi,{optional:!0,skipSelf:!0});_changeDetectorRef=s(Y);_expansionDispatcher=s(St);_openCloseAllSubscription=le.EMPTY;closed=new C;opened=new C;destroyed=new C;expandedChange=new C;id=s(ve).getId("cdk-accordion-child-");get expanded(){return this._expanded}set expanded(e){if(this._expanded!==e){if(this._expanded=e,this.expandedChange.emit(e),e){this.opened.emit();let t=this.accordion?this.accordion.id:this.id;this._expansionDispatcher.notify(this.id,t)}else this.closed.emit();this._changeDetectorRef.markForCheck()}}_expanded=!1;disabled=!1;_removeUniqueSelectionListener=()=>{};constructor(){}ngOnInit(){this._removeUniqueSelectionListener=this._expansionDispatcher.listen((e,t)=>{this.accordion&&!this.accordion.multi&&this.accordion.id===t&&this.id!==e&&(this.expanded=!1)}),this.accordion&&(this._openCloseAllSubscription=this._subscribeToOpenCloseAllActions())}ngOnDestroy(){this.opened.complete(),this.closed.complete(),this.destroyed.emit(),this.destroyed.complete(),this._removeUniqueSelectionListener(),this._openCloseAllSubscription.unsubscribe()}toggle(){this.disabled||(this.expanded=!this.expanded)}close(){this.disabled||(this.expanded=!1)}open(){this.disabled||(this.expanded=!0)}_subscribeToOpenCloseAllActions(){return this.accordion._openCloseAllActions.subscribe(e=>{this.disabled||(this.expanded=e)})}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["cdk-accordion-item"],["","cdkAccordionItem",""]],inputs:{expanded:[2,"expanded","expanded",y],disabled:[2,"disabled","disabled",y]},outputs:{closed:"closed",opened:"opened",destroyed:"destroyed",expandedChange:"expandedChange"},exportAs:["cdkAccordionItem"],features:[M([{provide:Mi,useValue:void 0}])]})}return a})(),un=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=X({type:a});static \u0275inj=K({})}return a})();var cr=["body"],lr=["bodyWrapper"],dr=[[["mat-expansion-panel-header"]],"*",[["mat-action-row"]]],mr=["mat-expansion-panel-header","*","mat-action-row"];function hr(a,n){}var pr=[[["mat-panel-title"]],[["mat-panel-description"]],"*"],ur=["mat-panel-title","mat-panel-description","*"];function fr(a,n){a&1&&(c(0,"span",1),lt(),c(1,"svg",2),B(2,"path",3),l()())}var Ei=new A("MAT_ACCORDION"),fn=new A("MAT_EXPANSION_PANEL"),_r=(()=>{class a{_template=s(G);_expansionPanel=s(fn,{optional:!0});constructor(){}static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["ng-template","matExpansionPanelContent",""]]})}return a})(),_n=new A("MAT_EXPANSION_PANEL_DEFAULT_OPTIONS"),gr=(()=>{class a extends pn{_viewContainerRef=s(re);_animationsDisabled=s(he,{optional:!0})==="NoopAnimations";_document=s(De);_ngZone=s(oe);_elementRef=s(F);_renderer=s(et);_cleanupTransitionEnd;get hideToggle(){return this._hideToggle||this.accordion&&this.accordion.hideToggle}set hideToggle(e){this._hideToggle=e}_hideToggle=!1;get togglePosition(){return this._togglePosition||this.accordion&&this.accordion.togglePosition}set togglePosition(e){this._togglePosition=e}_togglePosition;afterExpand=new C;afterCollapse=new C;_inputChanges=new N;accordion=s(Ei,{optional:!0,skipSelf:!0});_lazyContent;_body;_bodyWrapper;_portal;_headerId=s(ve).getId("mat-expansion-panel-header-");constructor(){super();let e=s(_n,{optional:!0});this._expansionDispatcher=s(St),e&&(this.hideToggle=e.hideToggle)}_hasSpacing(){return this.accordion?this.expanded&&this.accordion.displayMode==="default":!1}_getExpandedState(){return this.expanded?"expanded":"collapsed"}toggle(){this.expanded=!this.expanded}close(){this.expanded=!1}open(){this.expanded=!0}ngAfterContentInit(){this._lazyContent&&this._lazyContent._expansionPanel===this&&this.opened.pipe(ne(null),Te(()=>this.expanded&&!this._portal),Vi(1)).subscribe(()=>{this._portal=new _t(this._lazyContent._template,this._viewContainerRef)}),this._setupAnimationEvents()}ngOnChanges(e){this._inputChanges.next(e)}ngOnDestroy(){super.ngOnDestroy(),this._cleanupTransitionEnd?.(),this._inputChanges.complete()}_containsFocus(){if(this._body){let e=this._document.activeElement,t=this._body.nativeElement;return e===t||t.contains(e)}return!1}_transitionEndListener=({target:e,propertyName:t})=>{e===this._bodyWrapper?.nativeElement&&t==="grid-template-rows"&&this._ngZone.run(()=>{this.expanded?this.afterExpand.emit():this.afterCollapse.emit()})};_setupAnimationEvents(){this._ngZone.runOutsideAngular(()=>{this._animationsDisabled?(this.opened.subscribe(()=>this._ngZone.run(()=>this.afterExpand.emit())),this.closed.subscribe(()=>this._ngZone.run(()=>this.afterCollapse.emit()))):setTimeout(()=>{let e=this._elementRef.nativeElement;this._cleanupTransitionEnd=this._renderer.listen(e,"transitionend",this._transitionEndListener),e.classList.add("mat-expansion-panel-animations-enabled")},200)})}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=x({type:a,selectors:[["mat-expansion-panel"]],contentQueries:function(t,i,o){if(t&1&&E(o,_r,5),t&2){let r;_(r=g())&&(i._lazyContent=r.first)}},viewQuery:function(t,i){if(t&1&&(O(cr,5),O(lr,5)),t&2){let o;_(o=g())&&(i._body=o.first),_(o=g())&&(i._bodyWrapper=o.first)}},hostAttrs:[1,"mat-expansion-panel"],hostVars:4,hostBindings:function(t,i){t&2&&T("mat-expanded",i.expanded)("mat-expansion-panel-spacing",i._hasSpacing())},inputs:{hideToggle:[2,"hideToggle","hideToggle",y],togglePosition:"togglePosition"},outputs:{afterExpand:"afterExpand",afterCollapse:"afterCollapse"},exportAs:["matExpansionPanel"],features:[M([{provide:Ei,useValue:void 0},{provide:fn,useExisting:a}]),R,de],ngContentSelectors:mr,decls:9,vars:4,consts:[["bodyWrapper",""],["body",""],[1,"mat-expansion-panel-content-wrapper"],["role","region",1,"mat-expansion-panel-content",3,"id"],[1,"mat-expansion-panel-body"],[3,"cdkPortalOutlet"]],template:function(t,i){t&1&&(U(dr),S(0),c(1,"div",2,0)(3,"div",3,1)(5,"div",4),S(6,1),w(7,hr,0,0,"ng-template",5),l(),S(8,2),l()()),t&2&&(d(),V("inert",i.expanded?null:""),d(2),p("id",i.id),V("aria-labelledby",i._headerId),d(4),p("cdkPortalOutlet",i._portal))},dependencies:[it],styles:[`.mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;position:relative;background:var(--mat-expansion-container-background-color, var(--mat-sys-surface));color:var(--mat-expansion-container-text-color, var(--mat-sys-on-surface));border-radius:var(--mat-expansion-container-shape, 12px)}.mat-expansion-panel.mat-expansion-panel-animations-enabled{transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape, 12px);border-top-left-radius:var(--mat-expansion-container-shape, 12px)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape, 12px);border-bottom-left-radius:var(--mat-expansion-container-shape, 12px)}@media(forced-colors: active){.mat-expansion-panel{outline:solid 1px}}.mat-expansion-panel-content-wrapper{display:grid;grid-template-rows:0fr;grid-template-columns:100%}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content-wrapper{transition:grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{grid-template-rows:1fr}@supports not (grid-template-rows: 0fr){.mat-expansion-panel-content-wrapper{height:0}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{height:auto}}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;min-height:0;visibility:hidden;font-family:var(--mat-expansion-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-sys-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-sys-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-sys-body-large-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content{transition:visibility 190ms linear}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper>.mat-expansion-panel-content{visibility:visible}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-sys-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}
`],encapsulation:2,changeDetection:0})}return a})(),Rd=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["mat-action-row"]],hostAttrs:[1,"mat-action-row"]})}return a})(),br=(()=>{class a{panel=s(gr,{host:!0});_element=s(F);_focusMonitor=s(Me);_changeDetectorRef=s(Y);_parentChangeSubscription=le.EMPTY;constructor(){s(Ee).load(Pe);let e=this.panel,t=s(_n,{optional:!0}),i=s(new Je("tabindex"),{optional:!0}),o=e.accordion?e.accordion._stateChanges.pipe(Te(r=>!!(r.hideToggle||r.togglePosition))):ot;this.tabIndex=parseInt(i||"")||0,this._parentChangeSubscription=ae(e.opened,e.closed,o,e._inputChanges.pipe(Te(r=>!!(r.hideToggle||r.disabled||r.togglePosition)))).subscribe(()=>this._changeDetectorRef.markForCheck()),e.closed.pipe(Te(()=>e._containsFocus())).subscribe(()=>this._focusMonitor.focusVia(this._element,"program")),t&&(this.expandedHeight=t.expandedHeight,this.collapsedHeight=t.collapsedHeight)}expandedHeight;collapsedHeight;tabIndex=0;get disabled(){return this.panel.disabled}_toggle(){this.disabled||this.panel.toggle()}_isExpanded(){return this.panel.expanded}_getExpandedState(){return this.panel._getExpandedState()}_getPanelId(){return this.panel.id}_getTogglePosition(){return this.panel.togglePosition}_showToggle(){return!this.panel.hideToggle&&!this.panel.disabled}_getHeaderHeight(){let e=this._isExpanded();return e&&this.expandedHeight?this.expandedHeight:!e&&this.collapsedHeight?this.collapsedHeight:null}_keydown(e){switch(e.keyCode){case 32:case 13:ze(e)||(e.preventDefault(),this._toggle());break;default:this.panel.accordion&&this.panel.accordion._handleHeaderKeydown(e);return}}focus(e,t){e?this._focusMonitor.focusVia(this._element,e,t):this._element.nativeElement.focus(t)}ngAfterViewInit(){this._focusMonitor.monitor(this._element).subscribe(e=>{e&&this.panel.accordion&&this.panel.accordion._handleHeaderFocus(this)})}ngOnDestroy(){this._parentChangeSubscription.unsubscribe(),this._focusMonitor.stopMonitoring(this._element)}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=x({type:a,selectors:[["mat-expansion-panel-header"]],hostAttrs:["role","button",1,"mat-expansion-panel-header","mat-focus-indicator"],hostVars:13,hostBindings:function(t,i){t&1&&I("click",function(){return i._toggle()})("keydown",function(r){return i._keydown(r)}),t&2&&(V("id",i.panel._headerId)("tabindex",i.disabled?-1:i.tabIndex)("aria-controls",i._getPanelId())("aria-expanded",i._isExpanded())("aria-disabled",i.panel.disabled),Oe("height",i._getHeaderHeight()),T("mat-expanded",i._isExpanded())("mat-expansion-toggle-indicator-after",i._getTogglePosition()==="after")("mat-expansion-toggle-indicator-before",i._getTogglePosition()==="before"))},inputs:{expandedHeight:"expandedHeight",collapsedHeight:"collapsedHeight",tabIndex:[2,"tabIndex","tabIndex",e=>e==null?0:be(e)]},ngContentSelectors:ur,decls:5,vars:3,consts:[[1,"mat-content"],[1,"mat-expansion-indicator"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 -960 960 960","aria-hidden","true","focusable","false"],["d","M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z"]],template:function(t,i){t&1&&(U(pr),c(0,"span",0),S(1),S(2,1),S(3,2),l(),w(4,fr,3,0,"span",1)),t&2&&(T("mat-content-hide-toggle",!i._showToggle()),d(4),Q(i._showToggle()?4:-1))},styles:[`.mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;height:var(--mat-expansion-header-collapsed-state-height, 48px);font-family:var(--mat-expansion-header-text-font, var(--mat-sys-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-sys-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-sys-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-sys-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-sys-title-medium-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-header{transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header::before{border-radius:inherit}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height, 64px)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-sys-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-sys-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-sys-on-surface-variant))}.mat-expansion-panel-animations-enabled .mat-expansion-indicator{transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header.mat-expanded .mat-expansion-indicator{transform:rotate(180deg)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:"";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, none)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-header-indicator-display, inline-block)}@media(forced-colors: active){.mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}}
`],encapsulation:2,changeDetection:0})}return a})(),Sd=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["mat-panel-description"]],hostAttrs:[1,"mat-expansion-panel-header-description"]})}return a})(),Td=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275dir=f({type:a,selectors:[["mat-panel-title"]],hostAttrs:[1,"mat-expansion-panel-header-title"]})}return a})(),Id=(()=>{class a extends hn{_keyManager;_ownHeaders=new Fe;_headers;hideToggle=!1;displayMode="default";togglePosition="after";ngAfterContentInit(){this._headers.changes.pipe(ne(this._headers)).subscribe(e=>{this._ownHeaders.reset(e.filter(t=>t.panel.accordion===this)),this._ownHeaders.notifyOnChanges()}),this._keyManager=new je(this._ownHeaders).withWrap().withHomeAndEnd()}_handleHeaderKeydown(e){this._keyManager.onKeydown(e)}_handleHeaderFocus(e){this._keyManager.updateActiveItem(e)}ngOnDestroy(){super.ngOnDestroy(),this._keyManager?.destroy(),this._ownHeaders.destroy()}static \u0275fac=(()=>{let e;return function(i){return(e||(e=H(a)))(i||a)}})();static \u0275dir=f({type:a,selectors:[["mat-accordion"]],contentQueries:function(t,i,o){if(t&1&&E(o,br,5),t&2){let r;_(r=g())&&(i._headers=r)}},hostAttrs:[1,"mat-accordion"],hostVars:2,hostBindings:function(t,i){t&2&&T("mat-accordion-multi",i.multi)},inputs:{hideToggle:[2,"hideToggle","hideToggle",y],displayMode:"displayMode",togglePosition:"togglePosition"},exportAs:["matAccordion"],features:[M([{provide:Ei,useExisting:a}]),R]})}return a})(),Md=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=X({type:a});static \u0275inj=K({imports:[ee,un,oa]})}return a})();export{Ot as a,Bt as b,di as c,Ba as d,Na as e,Re as f,Qa as g,pi as h,No as i,Dc as j,ml as k,hl as l,pl as m,ul as n,fl as o,_l as p,gl as q,bl as r,vl as s,yl as t,wl as u,nn as v,rr as w,sr as x,gr as y,Rd as z,br as A,Sd as B,Td as C,Id as D,Md as E};
